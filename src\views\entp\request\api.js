import qs from 'qs'
import service from '@/api/request' // 引入axios封装

// 示例
export const requestAll = arr => Promise.all(arr)
export const getData = data => service({ url: `/entp/XXX`, method: 'get', params: data })
export const postData = data =>
  service({
    url: `/entp/XXX`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const getDictList = data => service({ url: `/entp/dict/list`, method: 'get', params: data })

// 个人信息相关
export const queryTeam = data => service({ url: `/entp/user/queryTeam`, method: 'get', params: data })
export const logout = data =>
  service({
    url: `/entp/logout`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 基础信息
export const getUserBasicInfo = data => service({ url: `/entp/user`, method: 'get', params: data })
export const editUserBasicInfo = data => service({ url: `/entp/user`, method: 'put', data })

// ------------------个人信息-教育信息-----------------------
export const addEducation = data => service({ url: `/entp/user/education`, method: 'post', data })
export const getEducation = data => service({ url: `/entp/user/education/page`, method: 'get', params: data })
export const delEducation = data => service({ url: `/entp/user/education`, method: 'delete', data })

// 工作履历
export const getExperience = data => service({ url: `/entp/user/experience/page`, method: 'get', params: data })
export const addExperience = data => service({ url: `/entp/user/experience`, method: 'post', data })
export const delExperience = data => service({ url: `/entp/user/experience`, method: 'delete', data })

// --------------------企业职层相关-接口---------------------------
export const getJobLevels = data => service({ url: `/entp/jobLevel`, method: 'get', params: data })
export const createJobLevel = data => service({ url: `/entp/jobLevel`, method: 'post', data })
export const updateJobLevel = data => service({ url: `/entp/jobLevel`, method: 'put', data })
export const deleteJobLevel = data => service({ url: `/entp/jobLevel`, method: 'delete', data })
export const searchJobLevel = data => service({ url: `/entp/jobLevel/page`, method: 'get', params: data })
export const jobLevelTree = data => service({ url: `/entp/jobLevel/tree`, method: 'get', params: data })
export const getJobLevelInfo = data => service({ url: `/entp/jobLevel/query`, method: 'get', params: data })

// 员工管理
export const getStaff = data => service({ url: `/entp/staff/page`, method: 'get', params: data })
export const createStaff = data => service({ url: `/entp/staff`, method: 'post', data })
export const delStaff = data => service({ url: `/entp/staff`, method: 'delete', data })
export const editStaff = data => service({ url: `/entp/staff`, method: 'put', data })
export const getStaffInfo = data => service({ url: `/entp/staff`, method: 'get', params: data })
export const readExcelData = data =>
  service({
    url: `/entp/staff/readExcelData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
export const importStaffData = data => service({ url: `/entp/staff/importData`, method: 'post', data })
export const checkEmployeeCode = data => service({ url: `/entp/user/checkEmployeeCode`, method: 'get', params: data })
export const checkPhoneNumber = data => service({ url: `/entp/user/checkPhoneNumber`, method: 'get', params: data })
export const checkEmail = data => service({ url: `/entp/user/checkEmail`, method: 'get', params: data })
export const modifyCurrentQuitDate = data =>
  service({
    url: `/entp/staff/modifyCurrentQuitDate`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const getSupName = data => service({ url: `/entp/staff/getSupName`, method: 'get', params: data })
export const exportDataConfirm = data =>
  service({
    url: `/entp/staff/exportDataConfirm`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 用户角色 相关
export const editUserRole = data =>
  service({
    url: `/entp/role/saveUserRole`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 岗位族群
export const getJobClassTree = data => service({ url: `/entp/jobClass/tree`, method: 'get', params: data })
export const addJobClass = data => service({ url: `/entp/jobClass`, method: 'post', data })
export const delJobClass = data => service({ url: `/entp/jobClass`, method: 'delete', data })
export const getJobClass = data => service({ url: `/entp/jobClass`, method: 'get', params: data })
export const updateJobClass = data => service({ url: `/entp/jobClass`, method: 'put', data })
export const getJobClassPage = data => service({ url: `/entp/jobClass/page`, method: 'get', params: data })

// ***********************账号密码***********************
export const editAccountInfo = data => service({ url: `/entp/user/modifyAccountPassword`, method: 'put', data })
export const getCode = data => service({ url: `/entp/code`, method: 'get', params: data })

//*************************职等管理********************************
export const addJobGrade = data => service({ url: `/entp/jobGrade`, method: 'post', data })
export const updateJobGrade = data => service({ url: `/entp/jobGrade`, method: 'put', data })
export const delJobGrade = data => service({ url: `/entp/jobGrade`, method: 'delete', data })
export const getJobGradePage = data => service({ url: `/entp/jobGrade/page`, method: 'get', params: data })
export const getJobGradeList = data => service({ url: `/entp/jobGrade`, method: 'get', params: data })

// --------------------岗位信息接口---------------------------
export const searchPost = data => service({ url: `/entp/post`, method: 'get', params: data })
export const createPost = data => service({ url: `/entp/post`, method: 'post', data })
export const updatePost = data => service({ url: `/entp/post`, method: 'put', data })
export const deletePost = data => service({ url: `/entp/post`, method: 'delete', data })
export const getPostCode = data => service({ url: `/entp/post/code`, method: 'get', params: data })
export const getPostList = data => service({ url: `/entp/post/list`, method: 'get', params: data })
export const getPostPageList = data => service({ url: `/entp/post/page`, method: 'get', params: data })
export const getPostRelationShip = data => service({ url: `/entp/post/relationShip`, method: 'get', params: data })
export const postPortraitList = data => service({ url: `/entp/post/postPortrait`, method: 'get', params: data })
export const getPostDictDef = data => service({ url: `/entp/post/dictDef`, method: 'get', params: data })
export const getPostTree = data => service({ url: `/entp/post/tree`, method: 'get', params: data })
export const checkPostLeader = data => service({ url: `/entp/post/checkIsLeader`, method: 'get', params: data })
export const postCapability = data => service({ url: `/entp/post/capabilityRequirements`, method: 'get', params: data })
export const importPostData = data => service({ url: `/entp/post/importData`, method: 'post', data })
export const exportPostData = data =>
  service({
    url: `/entp/post/exportData`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const exportDownload = data =>
  service({ url: `/entp/attach/export/download`, method: 'get', params: data, responseType: 'blob' })
export const readPostExcelData = data =>
  service({
    url: `/entp/post/readExcelData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })

// --------------------岗位职责接口---------------------------
export const getPostRespList = data => service({ url: `/entp/postResp`, method: 'get', params: data })
export const createPostResp = data => service({ url: `/entp/postResp`, method: 'post', data })
export const deletePostResp = data => service({ url: `/entp/postResp`, method: 'delete', data })

// --------------------协同岗位接口---------------------------
export const getPostCoop = data => service({ url: `/entp/postCoop`, method: 'get', params: data })
export const getDict = data => service({ url: `/entp/dict`, method: 'get', params: data })
export const batchCreatePostCoop = data => service({ url: `/entp/postCoop`, method: 'post', data })

// --------------------岗位工作活动---------------------------
export const getPostActivity = data => service({ url: `/entp/postActivity`, method: 'get', params: data })
export const createPostActivity = data => service({ url: `/entp/postActivity`, method: 'post', data })
export const deletePostActivity = data => service({ url: `/entp/postActivity`, method: 'delete', data })

// --------------------岗位参与流程---------------------------
export const createPostProcess = data => service({ url: `/entp/postProcess`, method: 'post', data })
export const getPostProcess = data => service({ url: `/entp/postProcess`, method: 'get', params: data })

// 企业工作活动
export const getJobActivity = data => service({ url: `/entp/jobActivity`, method: 'get', params: data })
export const addJobActivity = data => service({ url: `/entp/jobActivity`, method: 'post', data })
export const delJobActivity = data => service({ url: `/entp/jobActivity`, method: 'delete', data })
export const getJobActivityPage = data => service({ url: `/entp/jobActivity/page`, method: 'get', params: data })

// --------------------组织管理-组织层级---------------------------
export const getOrgLevelList = data => service({ url: `/entp/orgLevel/list`, method: 'get', params: data })
export const createOrgLevel = data => service({ url: `/entp/orgLevel`, method: 'post', data })
export const deleteOrgLevel = data => service({ url: `/entp/orgLevel`, method: 'delete', data })

// --------------------组织管理-组织职责---------------------------
export const getRespList = data => service({ url: `/entp/resp/page`, method: 'get', params: data })
export const createResp = data => service({ url: `/entp/resp`, method: 'post', data })
export const updateResp = data => service({ url: `/entp/resp`, method: 'put', data })
export const getRespInfo = data => service({ url: `/entp/resp`, method: 'get', params: data })
export const deleteResp = data => service({ url: `/entp/resp`, method: 'delete', data })
export const readOrgDutyExcelData = data =>
  service({
    url: `/entp/resp/readExcelData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
export const importOrgDuty = data => service({ url: `/entp/resp/import`, method: 'post', data })
export const exportOrgDuty = data =>
  service({
    url: `/entp/resp/export`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// --------------------组织管理-组织管理---------------------------
export const getOrgDeptInfo = data => service({ url: `/entp/org`, method: 'get', params: data })
export const createOrgDept = data => service({ url: `/entp/org`, method: 'post', data })
export const updateOrgDept = data => service({ url: `/entp/org`, method: 'put', data })
export const getOrgDeptList = data => service({ url: `/entp/org/page`, method: 'get', params: data })
export const getOrgDeptTree = data => service({ url: `/entp/org/tree`, method: 'get', params: data })
export const getOrgStaffList = data => service({ url: `/entp/staff/queryOrgStaffByLike`, method: 'get', params: data })
export const getCandidate = data => service({ url: `/entp/org/candidate`, method: 'get', params: data })
export const principal = data => service({ url: `/entp/org/principal`, method: 'get', params: data })
export const deleteOrgDept = data => service({ url: `/entp/org`, method: 'delete', data })
export const getNoOrgLeader = data => service({ url: `/entp/org/noOrgLeader`, method: 'get', params: data })
export const importOrgData = data => service({ url: `/entp/org/import`, method: 'post', data })
export const exportOrgData = data =>
  service({
    url: `/entp/org/export`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const readOrgExcelData = data =>
  service({
    url: `/entp/org/readExcelData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })

// --------------------组织管理-组织职责维护
export const getOrgRespList = data => service({ url: `/entp/orgResp/list`, method: 'get', params: data })
export const getOrgRespAllList = data => service({ url: `/entp/resp/list`, method: 'get', params: data })
export const setOrgResp = data => service({ url: `/entp/orgResp`, method: 'post', data })
export const deleteOrgResp = data => service({ url: `/entp/orgResp`, method: 'delete', data })
export const setOrgRespList = data => service({ url: `/entp/orgResp/listGroupByBizCode`, method: 'get', params: data })
export const setOrgRespCopy = data =>
  service({
    url: `/entp/orgResp/copy`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// --------------------组织管理-组织结构图---------------------------
export const getOrgStructure = data => service({ url: `/entp/org/structure`, method: 'get', params: data })

// --------------------企业设置-业务流程设置 zsf---------------------------
export const getProcessInfo = data => service({ url: `/entp/process`, method: 'get', params: data })
export const createProcess = data => service({ url: `/entp/process`, method: 'post', data })
export const getProcessList = data => service({ url: `/entp/process/page`, method: 'get', params: data })
export const getProcessTree = data => service({ url: `/entp/process/tree`, method: 'get', params: data })
export const updateProcess = data => service({ url: `/entp/process`, method: 'put', data })
export const deleteProcess = data => service({ url: `/entp/process`, method: 'delete', data })

// 企业详情接口
export const getCompanyInfo = data => service({ url: `/entp/company`, method: 'get', params: data })

//----------------------------企业设置--------------------------------------
export const createCompany = data => service({ url: `/entp/company`, method: 'post', data })
export const getCompany = data => service({ url: `/entp/company`, method: 'get', params: data })
export const updateCompany = data => service({ url: `/entp/company`, method: 'put', data })

// 上传
export const fileUpload = data =>
  service({
    url: `/entp/attach/upload`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
// 下载
export const fileDownload = data =>
  service({ url: `/entp/attach/download`, method: 'get', params: data, responseType: 'blob' })

//--------------------企业设置-业务领域设置---------------------------
export const getDomainList = data => service({ url: `/entp/domain/list`, method: 'get', params: data })
export const createDomain = data => service({ url: `/entp/domain`, method: 'post', data })
export const deleteDomain = data => service({ url: `/entp/domain`, method: 'delete', data })

// --------------------指标管理-指标维护---------------------------
export const getKpiClassTree = data => service({ url: `/entp/kpi/kpiClassTree`, method: 'get', params: data })
export const getKpiList = data => service({ url: `/entp/kpi/page`, method: 'get', params: data })
export const createKpi = data => service({ url: `/entp/kpi`, method: 'post', data })
export const updateKpi = data => service({ url: `/entp/kpi`, method: 'put', data })
export const getKpiInfo = data => service({ url: `/entp/kpi`, method: 'get', params: data })
export const deleteKpi = data =>
  service({
    url: `/entp/kpi`,
    method: 'delete',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const createKpiClass = data =>
  service({
    url: `/entp/kpi/saveKpiClass`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const getKpiClassDetail = data => service({ url: `/entp/kpi/kpiClassDetail`, method: 'get', params: data })
export const updateKpiClass = data =>
  service({
    url: `/entp/kpi/modifyKpiClass`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const deleteKpiClass = data => service({ url: `/entp/kpi/deleteKpiClass`, method: 'delete', data })
export const getCycleNbr = data => service({ url: `/entp/dict/queryValue`, method: 'get', params: data })

// --------------------指标管理-组织关联---------------------------
export const getOrgKpiList = data => service({ url: `/entp/orgKpi/page`, method: 'get', params: data })
export const relateOrgKpi = data =>
  service({
    url: `/entp/orgKpi`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// --------------------指标管理-岗位关联---------------------------
export const getPostKpiList = data => service({ url: `/entp/postKpi/page`, method: 'get', params: data })
export const relatePostKpi = data => service({ url: `/entp/postKpi`, method: 'get', params: data })
export const updatePostKpi = data =>
  service({
    url: `/entp/postKpi`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// --------------------指标管理-人员关联---------------------------
export const getKpiUserList = data => service({ url: `/entp/kpi/user/list`, method: 'get', params: data })
export const relateUserKpi = data =>
  service({
    url: `/entp/kpi/user`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// --------------------系统管理-角色权限---------------------------
export const getcompanyRoleList = data => service({ url: `/entp/role/companyRoleList`, method: 'get', params: data })
export const createdCompanyRole = data => service({ url: `/entp/role/saveCompanyRole`, method: 'post', data })
export const updateCompanyRole = data => service({ url: `/entp/role/modifyCompanyRole`, method: 'put', data })
export const deleteCompanyRole = data => service({ url: `/entp/role/deleteCompanyRole`, method: 'delete', data })
export const getRelationMenuTree = data =>
  service({ url: `/entp/role/queryRelationMenuTree`, method: 'get', params: data })
export const getMenuTreeAndUser = data =>
  service({ url: `/entp/role/queryMenuTreeAndUser`, method: 'get', params: data })
export const getLoginUserMenuTree = data =>
  service({ url: `/entp/role/queryLoginUserMenuTree`, method: 'get', params: data })
export const saveRoleMenuAndUser = data =>
  service({
    url: `/entp/role/saveRoleMenuAndUser`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const saveRoleAuthority = data =>
  service({
    url: `/entp/role/saveRoleAuthority`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const deleteRoleAuthority = data => service({ url: `/entp/role/deleteRoleAuthority`, method: 'delete', data })

// 操作日志接口-zsf
export const getLogList = data => service({ url: `/entp/log/page`, method: 'get', params: data })

// -----------------------职位管理---------------------------
export const queryJobInfoPage = data => service({ url: `/entp/jobInfo/queryJobInfo`, method: 'get', params: data })
export const jobInfoByCode = data => service({ url: `/entp/jobInfo/jobInfoByCode`, method: 'get', params: data })
export const insertJobInfo = data => service({ url: `/entp/jobInfo/insertJobInfo`, method: 'put', data })
export const updateJobInfo = data => service({ url: `/entp/jobInfo/updateJobInfo`, method: 'put', data })
export const delJobInfo = data => service({ url: `/entp/jobInfo/delJobInfo`, method: 'delete', data })
export const getJobCode = data => service({ url: `/entp/jobInfo/getJobCode`, method: 'get', params: data })
export const getJobInfoList = data => service({ url: `/entp/jobInfo/getJobInfo`, method: 'get', params: data })
export const queryJobDescription = data =>
  service({ url: `/entp/jobInfo/queryJobDescription`, method: 'get', params: data })
export const qualityRequirements = data =>
  service({ url: `/entp/jobInfo/qualityRequirements`, method: 'get', params: data })
export const jobCapabilityRequirements = data =>
  service({ url: `/entp/jobInfo/capabilityRequirements`, method: 'get', params: data })
export const jobRespInfo = data => service({ url: `/entp/jobResp/jobRespInfo`, method: 'get', params: data })
export const deleteJobRespInfo = data => service({ url: `/entp/jobResp/deleteJobRespInfo`, method: 'delete', data })
export const insertJobRespInfo = data => service({ url: `/entp/jobResp/insertJobRespInfo`, method: 'put', data })
export const jobBizProcessInfo = data =>
  service({ url: `/entp/jobBizProcess/jobBizProcessInfo`, method: 'get', params: data })
export const modifyJobBizProcess = data =>
  service({
    url: `/entp/jobBizProcess/modifyJobBizProcess`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const jobActivityList = data =>
  service({ url: `/entp/jobTitleActivity/jobTitleActivityInfo`, method: 'get', params: data })
export const createJobActivity = data =>
  service({ url: `/entp/jobTitleActivity/insertTitleActivity`, method: 'post', data })
export const delTitleActivity = data =>
  service({ url: `/entp/jobTitleActivity/delTitleActivity`, method: 'delete', data })
export const jobInfoCapabilityRequirements = data =>
  service({ url: `/entp/jobInfo/jobCapabilityRequirements`, method: 'get', params: data })
export const jobInfoQualityRequirement = data =>
  service({ url: `/entp/jobInfo/requirement/getMultipleOptionList`, method: 'get', params: data })
export const jobQualityRequirements = data =>
  service({ url: `/entp/jobInfo/jobQualityRequirements`, method: 'get', params: data })
export const getSearchJobList = data => service({ url: `/entp/jobInfo/getJobList`, method: 'get', params: data })
// 职位管理-导入职位
export const readJobExcelData = data =>
  service({
    url: `/entp/jobInfo/readExcelData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
export const importJobData = data => service({ url: `/entp/jobInfo/importData`, method: 'post', data })
export const exportJobData = data =>
  service({
    url: `/entp/jobInfo/exportData`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 职位管理-基本信息
export const jobInfoJobtree = data => service({ url: `/entp/jobInfo/standard/jobtree`, method: 'get', params: data })
export const jobInfoOrgList = data => service({ url: `/entp/jobInfo/standard/orgList`, method: 'get', params: data })
export const stJobList = data => service({ url: `/entp/jobInfo/standard/stJobList`, method: 'get', params: data })
// 汇报关系
export const reportRelationshipList = data => service({ url: `/entp/staff/report/page`, method: 'get', params: data })
export const readReportRelationshipExcelData = data =>
  service({
    url: `/entp/staff/report/readExcelData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
export const importReportRelationshipData = data => service({ url: `/entp/staff/report/import`, method: 'post', data })
export const exportReportRelationshipData = data =>
  service({ url: `/entp/staff/report/export`, method: 'get', params: data, responseType: 'blob' })
export const setSuperiorId = data =>
  service({
    url: `/entp/staff/report/setSuperiorId`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
