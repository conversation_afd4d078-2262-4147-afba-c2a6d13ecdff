<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in workData" :key="item.id">
      <div class="current_company fs12" v-if="index == 0">当前公司</div>
      <el-input class="item name" v-model.trim="item.companyName" placeholder="填写公司名称"></el-input>
      <el-date-picker
        class="item long"
        value-format="YYYY-MM-DD"
        v-model="item.beginDate"
        type="date"
        placeholder="选择入职日期"
      ></el-date-picker>
      <el-date-picker
        class="item long"
        :picker-options="pickerOptions"
        value-format="YYYY-MM-DD"
        @change="val => endDateChange(val, item)"
        v-model="item.endDate"
        type="date"
        placeholder="选择离职日期"
      ></el-date-picker>
      <el-select class="item" v-model="item.jobLevelCode" placeholder="请选择岗位职层">
        <el-option v-for="item in jobLevelOption" :key="item.code" :label="item.value" :value="item.code"></el-option>
      </el-select>
      <el-select class="item" v-model="item.highestJobLevel" placeholder="是否最高职层">
        <el-option label="是" value="Y"></el-option>
        <el-option label="否" value="N"></el-option>
      </el-select>
      <el-cascader
        class="item"
        :options="jobClassOption"
        placeholder="填选择岗位类型"
        v-model="item.jobClassCode"
        :change-on-select="true"
        :clearable="true"
        :filterable="true"
        :show-all-levels="false"
        :props="{ value: 'code', label: 'value', expandTrigger: 'hover' }"
        @change="val => handleChange(val, 'jobClassCode')"
      ></el-cascader>
      <el-select class="item" v-model="item.bizDomainCode" placeholder="请选择业务领域">
        <el-option
          v-for="item in bizDomainOption"
          :key="item.bizDomainCode"
          :label="item.bizDomainName"
          :value="item.bizDomainCode"
        ></el-option>
      </el-select>
      <el-select class="item short" v-model="item.postRelated" placeholder>
        <el-option
          v-for="item in yesOrNo"
          :key="item.dictCode"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item short" v-model="item.industryRelated" placeholder>
        <el-option
          v-for="item in yesOrNo"
          :key="item.dictCode"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <div class="item item_icon_wrap">
        <el-icon class="item_icon" @click="() => deleteItem(item, index)"><Delete /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { jobLevelTree, getJobClassTree, getDomainList } from '../../../request/api'
import { useUserStore } from '@/stores/modules/user'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  workData: {
    type: Array,
    default: () => [{}]
  }
})

const emit = defineEmits(['deleteItem'])

const userStore = useUserStore()
const userId = computed(() => userStore.userInfo.userId)
const companyId = computed(() => userStore.userInfo.companyId)

const endDateType = ref('date')
const pickerOptions = {
  shortcuts: [
    {
      text: '至今',
      onClick: function (picker) {
        picker.$emit('pick', '')
      }
    }
  ]
}
const jobLevelOption = ref([])
const jobClassOption = ref([])
const bizDomainOption = ref([])
const yesOrNo = ref([])

function deleteItem(item, index) {
  emit('deleteItem', item, index)
}

function jobLevelTreeFun() {
  jobLevelTree().then(res => {
    jobLevelOption.value = window.$util.formatterData(res)
  })
}
function getJobClassTreeFun() {
  getJobClassTree().then(res => {
    jobClassOption.value = window.$util.formatterData(res)
  })
}
function getDomainListFun() {
  let params = {
    companyId: companyId.value,
    rStatus: 'Y'
  }
  getDomainList(params).then(res => {
    if (res.code == '200') {
      bizDomainOption.value = res.data
    }
  })
}
function handleChange(codeArr, type) {
  // 业务逻辑
}
function endDateChange(val, rowData) {
  let data = window.$util.deepClone(rowData)
  if (val) {
    if (!window.$util.compareDate(data.endDate, data.beginDate)) {
      window.$msg.warning('结束日期需大于开始日期')
      rowData.endDate = ''
    }
  }
}

watch(
  companyId,
  val => {
    if (val) {
      getDomainListFun()
    }
  },
  { immediate: true }
)

onMounted(() => {
  // window.$getDocList(['YES_NO', 'JOB_LEVEL']).then(res => {
  //   yesOrNo.value = res.YES_NO
  //   // jobLevelOption.value = res.JOB_LEVEL
  // })
  getJobClassTreeFun()
  getDomainListFun()
  jobLevelTreeFun()
})
</script>

<style scoped lang="scss">
.edu_info_item {
  padding-left: 50px;
  position: relative;
  .current_company {
    left: 5px;
    top: 15px;
    position: absolute;
    color: #0099fd;
    line-height: 14px;
  }
  .item {
    width: 15%;
    padding: 0 4px;
    &.name {
      width: 20%;
    }
    &.date {
      width: 30%;
    }
    &.short {
      width: 7%;
    }
  }
  .item_icon_wrap {
    text-align: center;
    margin-top: 3px;
    width: 5%;
    .item_icon {
      font-size: 20px;
      color: #fc2d11;
      cursor: pointer;
    }
  }
}
</style>
