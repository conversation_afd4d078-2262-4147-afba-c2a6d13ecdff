<template>
  <div class="bg_write">
    <div class="page_main_title">
      <div class="goback_geader" @click="goback"><i class="el-icon-arrow-left"></i>返回</div>
      部门盘点（ 点评下级 ）--{{ enqName }}
    </div>
    <div class="page_section">
      <div class="talent_raview_main padd_TB_16">
        <step-bar
          :needClick="enqOrgStatus == 'P'"
          @stepClick="stepClick"
          :stepData="stepData"
          :currentIndex="currentModuleCode"
        ></step-bar>
        <component
          :is="moduleObj[currentModuleCode]"
          :nextBtnText="nextBtnText"
          :enqId="enqId"
          @nextStep="nextStep"
          @prevStep="prevStep"
        ></component>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getEnqInfo, submitOrgInfo } from '../../request/api'
import { useUtils } from '@/utils/utils'
import stepBar from '@/components/talent/stepsComps/stepBar'
import personnelEvaluation from './departmentReviewComponents/personnelEvaluationPage'
import personnelCalibration from './departmentReviewComponents/personnelCalibrationPage'

const route = useRoute()
const utils = useUtils()
const enqOrgStatus = ref(null)
const enqId = ref(route.query.enqId)
const enqName = ref('')
const moduleArr = ref(['D08'])
const moduleObj = {
  D08: personnelEvaluation,
  D09: personnelCalibration
}
const currentModuleCode = ref('D08')
const currentIndex = ref(0)
const nextBtnText = ref('下一步')
const stepData = ref([
  {
    name: '人员评价',
    code: 'D08',
    enqProgress: 'N'
  }
  // {
  //     name:'人员校准',
  //     code:'D09',
  //     enqProgress:'N'
  // },
])

const goback = () => {
  utils.goback()
}

const getEnqInfoFun = async () => {
  const res = await getEnqInfo({ id: enqId.value })
  if (res.code == 200) {
    enqName.value = res.data.enqName
  }
}

const stepClick = (code, index) => {
  currentIndex.value = index
}

const nextStep = () => {
  const params = {
    enqId: enqId.value,
    module: currentModuleCode.value
  }
  stepData.value[currentIndex.value].enqProgress = 'Y'
  if (currentIndex.value == stepData.value.length - 1) {
    // 最后一步提交
    console.log('最后一步')
    utils.goback()
    return false
  }
  currentIndex.value++
  // 设置按钮文本 "下一步" or "提交"
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const prevStep = () => {
  if (currentIndex.value == 0) {
    return false
  }
  currentIndex.value--
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const submitOrgInfoFun = async () => {
  let params = {
    enqId: enqId.value
  }
  submitOrgInfo(params).then(res => {
    if (res.code == '200') {
      utils.goback()
    }
  })
}

watch(currentIndex, () => {
  currentModuleCode.value = moduleArr.value[currentIndex.value]
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
})

onMounted(() => {
  getEnqInfoFun()
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
})
</script>

<style scoped lang="scss">
.talent_raview_main {
  .talent_raview_btn_wrap {
    text-align: center;
    padding-top: 26px;
  }
}

.from_wrap {
  .basic_info {
    float: left;
    width: 50%;
  }

  .post_info {
    overflow: hidden;
  }

  .el-input__inner {
    width: 280px;
  }
}

.oper_btn_wrap {
  padding-top: 32px;
  text-align: center;
  &.align_right {
    text-align: right;
  }
}
// 去除input number类型 加减箭头
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input {
  -moz-appearance: textfield;
}
</style>
