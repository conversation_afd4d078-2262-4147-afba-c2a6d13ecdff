<template>
  <div class="basic_info_wrap bg_write page_main">
    <div class="page_main_title">基础信息</div>
    <div class="page_section basic_info_center clearfix">
      <div class="from_wrap clearfix">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleFormRef"
          label-width="110px"
          :validate-on-rule-change="false"
          class="demo-ruleForm clearfix flex_row_between"
        >
          <div class="basic_info el-col-12 section-border">
            <div class="page_second_title">基本信息</div>
            <div class="form_wrap list_from">
              <el-form-item label="姓名" prop="userName">
                <el-input v-model="ruleForm.userName"></el-input>
              </el-form-item>
              <el-form-item label="性别" prop="gender">
                <el-select v-model="ruleForm.gender" placeholder="请选择性别">
                  <el-option
                    v-for="item in genderOptions"
                    :label="item.codeName"
                    :value="item.dictCode"
                    :key="item.dictCode"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="出生日期" prop="birthday">
                <el-date-picker
                  v-model="ruleForm.birthday"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="籍贯" prop="nativePlace">
                <el-cascader
                  :options="cityInfo"
                  v-model="ruleForm.nativePlace"
                  :change-on-select="true"
                  :clearable="true"
                  :filterable="true"
                  :props="{ expandTrigger: 'hover' }"
                  @change="handleChange($event, 'nativePlace')"
                >
                </el-cascader>
              </el-form-item>
              <el-form-item label="家庭所在地" prop="homePlace">
                <el-cascader
                  :options="cityInfo"
                  v-model="ruleForm.homePlace"
                  :change-on-select="true"
                  :clearable="true"
                  :filterable="true"
                  :props="{ expandTrigger: 'hover' }"
                  @change="handleChange($event, 'homePlace')"
                >
                </el-cascader>
              </el-form-item>
              <el-form-item label="现常住地" prop="residencePlace">
                <el-cascader
                  :options="cityInfo"
                  v-model="ruleForm.residencePlace"
                  :change-on-select="true"
                  :clearable="true"
                  :filterable="true"
                  :props="{ expandTrigger: 'hover' }"
                  @change="handleChange($event, 'residencePlace')"
                >
                </el-cascader>
              </el-form-item>
              <el-form-item label="工作地址" prop="workPlace">
                <el-cascader
                  :options="cityInfo"
                  v-model="ruleForm.workPlace"
                  :change-on-select="true"
                  :clearable="true"
                  :filterable="true"
                  :props="{ expandTrigger: 'hover' }"
                  @change="handleChange($event, 'workPlace')"
                >
                </el-cascader>
              </el-form-item>
              <el-form-item label="民族" prop="nationalityCode">
                <el-select v-model="ruleForm.nationalityCode" filterable clearable>
                  <el-option
                    v-for="item in nationalityOptions"
                    :label="item.codeName"
                    :value="item.dictCode"
                    :key="item.dictCode"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="婚姻状况" prop="maritalStatus">
                <el-select v-model="ruleForm.maritalStatus">
                  <el-option
                    v-for="item in maritalOptions"
                    :label="item.codeName"
                    :value="item.dictCode"
                    :key="item.dictCode"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="post_info el-col-12">
            <div class="page_second_title">岗位信息</div>
            <div class="form_wrap list_from">
              <el-form-item label="所在部门" prop="orgName">
                <el-input v-model="ruleForm.orgName" disabled></el-input>
              </el-form-item>
              <el-form-item label="岗位名称" prop="postName">
                <el-input v-model="ruleForm.postName" disabled></el-input>
              </el-form-item>
              <el-form-item label="上级岗位" prop="parentPostName">
                <el-input v-model="ruleForm.parentPostName" disabled></el-input>
              </el-form-item>
            </div>
          </div>
        </el-form>
        <div class="align_center">
          <el-button type="primary" class="page_confirm_btn" @click="submitForm">确认</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { regionData } from 'element-china-area-data'
import { getUserBasicInfo, editUserBasicInfo, getDict } from '../../request/api'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()

const maritalOptions = ref([])
const genderOptions = ref([])
const cityInfo = ref(regionData)
const nationalityOptions = ref([])

const ruleForm = reactive({
  userName: '',
  gender: '',
  birthday: '',
  nativePlace: '',
  homePlace: '',
  residencePlace: '',
  workPlace: '',
  nationalityCode: '',
  maritalStatus: '',
  orgName: '',
  postName: '',
  parentPostName: ''
})

const rules = {
  userName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 1, message: '长度最少1个字符', trigger: 'blur' }
  ],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  nativePlace: [{ required: true, message: '请选择籍贯', trigger: 'change' }],
  homePlace: [{ required: true, message: '请选择家庭所在地', trigger: 'change' }],
  residencePlace: [{ required: true, message: '请选择常住驻地', trigger: 'change' }],
  workPlace: [{ required: true, message: '请选择工作地', trigger: 'change' }],
  birthday: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
  nationalityCode: [{ required: true, message: '请选择民族', trigger: 'change' }],
  maritalStatus: [{ required: true, message: '请选择婚姻状况', trigger: 'change' }]
}

const ruleFormRef = ref(null)

const userId = computed(() => userStore.userInfo.userId)

function handleChange(code, type) {
  ruleForm[type] = code[code.length - 1]
}

function getData() {
  getUserBasicInfo().then(res => {
    if (res.code == '200') {
      let data = res.data
      Object.keys(ruleForm).forEach(key => {
        ruleForm[key] = data[key]
      })
    } else {
      ElMessage({
        showClose: true,
        message: '获取用户信息失败！',
        type: 'error'
      })
    }
  })
}

function submitForm() {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      ruleForm.userId = userId.value
      editUserBasicInfo(ruleForm).then(res => {
        if (res.code == '200') {
          ElMessage({
            showClose: true,
            message: '保存成功！',
            type: 'success'
          })
        } else {
          ElMessage.error('保存失败！')
        }
      })
    }
  })
}

onMounted(() => {
  getData()
  getDict({ dictId: 'NATIONALITY_CODE' }).then(res => {
    if (res.code == '200') {
      nationalityOptions.value = res.data
    }
  })
  // proxy.$getDocList(['NATIONALITY_CODE', 'MARITAL_STATUS', 'GENDER']).then(res => {
  //   nationalityOptions.value = res.NATIONALITY_CODE
  //   maritalOptions.value = res.MARITAL_STATUS
  //   genderOptions.value = res.GENDER
  // })
})
</script>

<style scoped lang="scss">
.basic_info_wrap {
  width: 100%;
  // height: 800px;
  background: transparent;

  .form_wrap {
    margin-top: 40px;
  }
}

.demo-ruleForm {
  align-items: flex-start;
  /*border-bottom: 1px solid #e9eaeb;*/
  margin-bottom: 20px;
}
</style>
