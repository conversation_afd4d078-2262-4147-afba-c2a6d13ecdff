// 人才相关的样式 在每个模块的入口文件中引入
/*基础样式*/

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    width: 100%;
    // height: 100%;
    min-height: 100%;
    font-family: PingFang SC, Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    color: #212121;
    font-size: 14px;
    background-color: #F5F7FA;
    min-height: 100%;
}

input {
    outline: none;

    &::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #bbb;
    }
	&:focus{outline:none;}
}


// 内容文字色
.center_color {
    color: #525E6C;
}


// 占位文字色
.placeholder_color {
    color: #C0C4CC;
}


// 图标背景色
.icon_bg {
    background-color: #DAEFFF;
}


// 主色
.main_color {
    color: #0099FF;
}

.danger_color{
    color: #FF1413;
}

// 已完成
.completed_color {
    color: #0099FF;
}


a {
    text-decoration: none;
    color: #333;
}

.fl {
    float: left;
}

.fr {
    float: right;
}
.relative{
    position: relative;
}
.inline_b{
	display: inline-block;
}
.overflow_hidden {
    overflow: hidden;
}
.overflow_x_hidden {
    overflow-x: hidden;
}
.overflow_y_hidden {
    overflow-y: hidden;
}

.align_left {
    text-align: left;
}

.align_center {
    text-align: center;
}

.align_right {
    text-align: right;
}

.clearfix:after {
    content: '';
    display: block;
    clear: both;
    height: 0;
    visibility: hidden;
}

.clearfix {
    zoom: 1;
}

ul,
li {
    list-style: none;
}

.pointer {
    cursor: pointer;
}
.event_none{
    pointer-events: none;
}
.bg_none {
    background-color: none !important;
    background: none !important;
}
.text_right{
	text-align: right;
}
.text_left{
	text-align: left;
}
.text_center{
	text-align: center;
}


.overflow_elps {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.two_overflow_elps {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.three_overflow_elps {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.four_overflow_elps {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
}

// 内间距
.padd_LR_10 {
    padding-left: 10px;
    padding-right: 10px;
}

.padd_LR_16 {
    padding-left: 16px;
    padding-right: 16px;
}
.padd_R_10{
    padding-right: 10px;

}
.padd_TB_16 {
    padding-top: 16px;
    padding-bottom: 16px;
}

.paddT_12 {
    padding-top: 12px !important;
}
.paddT_30 {
	padding-top: 30px !important;
}
.padd_TB_30{
	padding-top: 30px !important;
	padding-bottom: 30px !important;
}


// 外间距
.marginT_8 {
    margin-top: 8px;
}
.marginR_16 {
    margin-right: 16px;
}

.marginL_16 {
    margin-left: 16px;
}

.marginT_16 {
    margin-top: 16px;
}

.marginB_16 {
    margin-bottom: 16px;
}
.marginB_32 {
    margin-bottom: 32px;
}
.marginT_20 {
	margin-top: 20px;
}
.marginT_30 {
	margin-top: 30px;
}

.border_r_1{
	border-right: 1px solid #e5e5e5;
}
.border_l_1{
	border-left: 1px solid #e5e5e5;
}
.border_none{
    border: none !important;
}
// 颜色
.color_666 {
    color: #666;
}

.color_danger {
    color: #FC2D11 !important;
}
.color_base{
	color: #0099FF;
}

// 字号
.fs12 {
    font-size: 12px !important;
}

.fs14 {
    font-size: 14px !important;
}

.fs16 {
    font-size: 16px !important;
}

.fs20 {
    font-size: 20px !important;
}
.fs21 {
	font-size: 21px !important;
}
.bold {
    font-weight: bold;
}

// css控制禁止选中文本
.noselect {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Chrome/Safari/Opera */
    -khtml-user-select: none; /* Konqueror */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently
    not supported by any browser */
}

// flex 

.flex_row_wrap_between {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	align-items: center;
}

//  row  不换行 两端对齐
.flex_row_between {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    align-items: center;
}
.flex_row_end {
	display: flex;
	flex-flow: row nowrap;
	justify-content: flex-end;
}
// row  不换行 两端对齐 不垂直居中
.flex_row_betweens {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    // align-items: center;
}

//  row  不换行 两端有间隔对齐 垂直居中
.flex_row_around {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-around;
    // align-items: center;
}

// row 不换行 起点对齐
.flex_row_start {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    // align-items: center;
}

//  row  换行 起点对齐
.flex_row_wrap_start {
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
    // align-items: center;
}

// col 不换行 起点对齐
.flex_col_start {
    display: flex;
    flex-flow: column nowrap;
    justify-content: flex-start;
}

// 分页插件 右对齐
.pagination_wrap {
    padding-top: 20px;
    text-align: right;
}

// 滚动条样式
/*chrome滚动条样式*/
::-webkit-scrollbar {
    /*滚动条整体部分，其中的属性有width,height,background,border（就和一个块级元素一样）等。*/
    width: 8px;
    height: 10px;
}

// ::-webkit-scrollbar-button {
    // /*滚动条两端的按钮。可以用display:none让其不显示，也可以添加背景图片，颜色改变显示效果。*/
    // display: none;
// }

// ::-webkit-scrollbar-track {
//     /*外层轨道。可以用display:none让其不显示，也可以添加背景图片，颜色改变显示效果。*/
//     // display: none;
// }

::-webkit-scrollbar-track-piece {
    /*内层轨道，滚动条中间部分（除去）。*/
    background: rgb(240, 240, 240);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    /*滚动条里面可以拖动的那部分*/
    background: rgb(200, 200, 200);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    /*滚动条里面可以拖动的那部分*/
    background: rgb(180, 180, 180);
}

::-webkit-scrollbar-corner {
    /*边角*/
    background: rgb(200, 200, 200);
}

.bg_write{
	height: 100%;
	background: #fff;
	overflow-y:auto;
}
// 页面主体内容布局
.page_container {
    width: 100%;
    margin: 0 auto;
    position: relative;

    .page_title {
        padding-left: 24px;
        color: #525E6C;
        font-size: 20px;
        font-weight: bold;
        line-height: 70px;
    }

    .page_main {
        position: relative;
		width: 100%;
		// height:800px;
		// min-height:800px;
        overflow-x: hidden;
        padding: 0 15px;


        .page_main_title {
			position: relative;
            padding-left: 30px;
            color: #555;
            font-size: 16px;
            font-weight: bold;
            line-height: 70px;
			&::before{
				position: absolute;
				left: 20px;
				top: 26px;
				content: "";
				width: 5px;
				height: 16px;
				border-radius: 5px;
				background: #0099FF;
			}
            .text {
                width: 150px;
            }

            .goback_geader {
                font-size: 14px;
                color: #525E6C;
                font-weight: normal;
                float: right;
                margin-right: 20px;
                padding-right: 20px;
                //border-right: 1px solid #e4e7ed;
                line-height: 20px;
                margin-top: 25px;
                cursor: pointer;
				color: #0099FF;
            }
        }

        .page_third_title {
			position: relative;
			font-size: 14px;
			line-height: 30px;
			padding-left: 20px;
			margin-bottom: 10px;
			&::before{
				position: absolute;
				left: 5px;
				top: 9px;
				content: "";
				width:10px;
				height: 10px;
				border-radius: 50%;
				background: #0099FF;
			}

			.text {
				width: 150px;
			}



            //position: relative;
            //font-size: 15px;
            //line-height: 30px;
            //color: #212121;
            //margin-bottom: 10px;
            ////font-weight: bold;
            //padding-bottom: 5px;
            //.text {
            //    width: 150px;
            //}
        }

        .page_section {
            padding:0 16px;
            overflow-x: hidden;
            .page_section_aside {
                float: left;
                width: 220px;
                margin-right: 16px;
				border: 1px solid #e5e5e5;
				.aside_tree_title{
					padding: 0 16px;
					height: 35px;
					line-height: 35px;
					background: #EBF4FF;
					.tree_title{
						width: 80px;
					}
				}
				.aside_tree_list{
					padding: 10px 16px;
					height: 640px;
					overflow-y: auto;
				}
            }

            .page_section_main {
                overflow-x: hidden;
				border: 1px solid #E5E5E5;
				min-height: 680px;
            }

            .page_section_title {
                font-size: 16px;
                line-height: 28px;
                color: #0099fd;
                font-weight: bold;
                padding-left: 8px;
                margin-bottom: 9px;
            }
        }

        .page_padding {
            padding: 16px;
        }

        .page_shadow {
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
            padding: 16px;
        }
    }
}
.oper_btn_wrap{
	margin-bottom: 16px;
	text-align: right;
}
.page_second_title {
	position: relative;
	font-size: 14px;
	line-height: 30px;
	padding-left: 20px;
	&::before{
		position: absolute;
		left: 5px;
		top: 10px;
		content: "";
		width:10px;
		height: 10px;
		background: #0099FF;
        z-index: 3;
	}

	.text {
		width: 150px;
	}
}

.edu_info_header {
	display: flex;
	flex-flow: row nowrap;
	justify-content: space-between;
	background: #f4f4f4;
	color: #525e6c;
	font-size: 14px;
	padding: 0 16px;
	height: 45px;
	line-height: 45px;
	.item{
		padding: 0 4px;
		line-height: 45px;
	}

}
.edu_info_item{
	display: flex;
	flex-flow: row nowrap;
	justify-content: space-between;
	padding: 7px 16px;
	.item{
		padding: 0 4px;
		line-height: 30px;
		font-size: 12px;
		.el-input__inner{
			font-size: 12px;
		}
	}
	&:nth-child(even){
		background: #f4f4f4;
	}
}



// 表单格式 内容信息展示dom

.info_display_wrap {
    width: 100%;

    .info_display_title {
        font-size: 16px;
        line-height: 26px;
        color: #0099fd;
        font-weight: bold;
        margin-bottom: 9px;
    }

    .info_display_item_box {
        .info_display_item {
            height: 30px;
            line-height: 30px;
            margin-bottom: 8px;
            font-size: 14px;

            &_label {
                line-height: 30px;
                color: #525E6C;
                float: left;
                width: 60px;
                text-align: left;
            }

            &_value {
                color: #c0c4cc;
                overflow: hidden;
            }
        }
    }
}

// 暂无数据样式
.no_data_tip {
    margin: 50px auto;
    text-align: center;
	color: #909399;
	font-size: 12px;
}
.no_data_row{
	margin: 20px auto;
	text-align: center;
	color: #909399;
	font-size: 12px;
}


// 禁用input去除背景颜色
.bg_none.el-input.is-disabled .el-input__inner {
    background-color: transparent;
}
// 表单内input select宽度定为280px;
.form_wrap{
	.el-input__inner {
		font-size: 14px;
	}
	&.list_from{
		.el-input__inner {
			width: 280px;
		}
	}
}
.el-form-item__label{
	line-height: 30px;
}
.el-form-item__content{
	line-height: 30px;
    .el-select{
        width: 190px;
    }
}
.el-cascader{
	line-height: 30px;
}
.el-input__inner {
	height: 30px;
	line-height: 30px;
	border-color: #e5e5e5;
	font-size: 12px;
}
.el-textarea__inner{
	font-size: 12px;
	font-family: PingFang SC, Avenir, Helvetica, Arial, sans-serif;
}
.el-input__suffix{
	height: auto;
}
.el-input__icon{
	line-height: 30px;
}

.el-form-item {
    margin-bottom: 20px;
}
.el-dialog__title{
	font-size: 16px;
}
.el-table__header tr th{
	background-color: #f4f4f4 !important;
	font-weight: normal;
	color: #212121;
	padding: 11px 0;
}
.el-table__body tr{
	&:nth-child(even){
		background: #f4f4f4;
	}
	td{
		padding: 0;
		font-size: 12px;
	}
}
.el-table tr{
	height: 35px;
}
// 表格行被选中时的背景色
.el-table__body tr.current-row>td{
    background-color: #81ddff;
}
.el-pager li.active{
	color: #0099FF;
}
.el-select-dropdown__item.selected{
	color: #0099FF;
}
.el-date-editor {
	font-size: 12px;
	.el-range-separator{
		width: 8%;
	}
}
.el-tabs__item:hover{
	color: #0099FF;
}
.el-tabs__item.is-active{
	color: #0099FF;
}
.el-tabs__active-bar{
	background-color: #0099FF;
}


.el-loading-spinner i{
	font-size: 24px;
	color: #f2f2f2;
}
.el-loading-spinner{
	.el-loading-text{
		color: #eee;
		font-size: 14px;
	}
}

.icon_del{
	font-size: 20px;
	color: #FC2D11 !important;
}
.icon_plus{
	i{
		font-weight: 900;
		font-size: 20px;
		color: #0099FF;
	}
}
.icon_edit{
	font-size: 20px;
	color: #0099FF;
	margin-right:10px;
	//display: block;
	//width: 18px;
	//height: 18px;
	//background: url("../../public/icons/icon_edit.png") no-repeat center;
}
.icon_detail{
	font-size: 20px;
	color: #0099FF;
	margin-right:10px;
}
.el-button+.el-button{
	margin-left: 5px;
}
.el-button--text{
	color: #0099FF;
}
.el-button--primary.is-plain{
	background: #EBF4FF;
	border-color: #0099FF;
	color: #0099FF;
}

.el-dialog__header{
	text-align: left;
}
.el-tree-node__label{
	font-size: 12px;
}
.el-range-editor.el-input__inner{
	padding: 0 10px;
}
.el-cascader-panel .el-radio {
    width: 100%;
    height: 100%;
    z-index: 10;
    position: absolute;
    top: 10px;
    right: 0px;
    padding: 0 10px;
    // background:pink;
    .el-radio__input{
        display: none;
    }
    &.is-disabled + .el-cascader-node__label{
        color: #C0C4CC;
    }
  }
  .el-cascader-panel .in-checked-path{
    .el-cascader-node__label{
        color: #409EFF !important;
    }
  }
.page_confirm_btn{
	width: 150px;
	height: 40px;
	background: #449CFF;
	border-radius: 0;
}
.page_new_confirm_btn{
	min-width: 100px;
	height: 35px;
    line-height: 35px;
    padding: 0 8px;
	background: #449CFF;
	border-radius: 0;
}
.page_add_btn{
	min-width: 80px;
	height: 30px;
	border-radius: 0;
	font-size: 14px;
	line-height: 30px;
	padding: 0 8px;
	background: #449CFF;
	color: #fff;
	border-color:#449CFF;
	cursor: pointer;
}
button.el-button:hover,.el-button.is-plain:hover{
	background: #0099FF;
	color: #fff;
}
button.el-button--text:hover{
	background:transparent;
	color: #449CFF;
	opacity: 0.8;
}
.page_clear_btn{
	width: 80px;
	height: 30px;
	border-radius: 0;
	font-size: 16px;
	line-height: 30px;
	padding: 0;
	background:transparent;
	&:hover{
		background: #fff;
		color: #fff;
	}
}

// 内容区域左侧 分类筛选

.page_section_filter_wrap {
    width: 240px;
    padding: 10px 32px 0 16px;
    margin-right: 36px;
    &.border {
        border-right: 1px solid #e4e7ed;
    }

    .page_section_filter_title {
        font-size: 16px;
        line-height: 26px;
        color: #0099fd;
        font-weight: bold;
        padding-left: 8px;
        margin-bottom: 9px;
    }

    .page_section_filter_item {
        text-align: center;
        background-color: #e4e7ed;
        margin-bottom: 16px;
        line-height: 40px;
        font-size: 14px;
        font-weight: 500;
        color: #525e6c;
        border-radius: 4px;
        cursor: pointer;

        &.active {
            background-color: #0099fd;
            color: #fff;
        }
    }
}

// 内容区域筛选 过滤 
.filter_bar_wrap {
    line-height: 32px;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 16px;

    .filter_item {
        margin-right: 8px;
        display: flex;
        flex-flow: row nowrap;
        justify-content: flex-start;
        // width: 190px;
        &.title {
            width: 34px;
            min-width: 34px;
            max-width: 34px;
        }
        &:last-of-type {
            margin-right: 0;
        }
        .filter_item_label{
            margin-right: 8px;
        }
        .filter_item_content{
            
        }
        .el-select{
            width: 190px;
        }
    }
}

.org_management_aside{
	border: 1px solid #E5E5E5;
	min-height: 680px;
	.aside_tree_title{
		padding: 0 16px;
		height: 35px;
		line-height: 35px;
		background: #EBF4FF;
	}
	.aside_tree_list{
		padding: 10px 16px;
	}
}
// 必填项星号
.required_icon{
    color: #F56C6C; 
}

// 表单模式布局
.form_dom {
    .form_row {
        // 行
        
        margin-bottom: 32px;
        .form_label {
            // label
            width: 100px;
            float: left;
            line-height: 30px;
            color: #525e6c;
        }
        .form_row_center {
            // 行内容
            display: flex;
            flex-flow: row nowrap;
            justify-content: flex-start;
            overflow: hidden;
            .row_center_item {
                margin-right: 16px;
                align-items: flex-start;
                .list {
                    line-height: 30px;
                    margin-right: 30px;
                    .title {
                        color: #0099fd;
                        font-size: 14px;
                        font-weight: bold;
                    }
                }
            }
        }
    }
}