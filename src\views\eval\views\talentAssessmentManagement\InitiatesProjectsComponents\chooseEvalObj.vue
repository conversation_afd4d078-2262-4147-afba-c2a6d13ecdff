<template>
  <div class="choose_testee_wrap" :class="{ disable: !isEdit }">
    <p class="page_second_title">选择人员</p>
    <div class="choose_testee_main flex_row_betweens">
      <div class="main_left">
        <div class="flex_row_start marginT_20">
          <div class="main_left_tree page_area">
            <p class="choose_testee_title">部门</p>
            <div class="second_level_post_wrap">
              <treeCompRadio
                class="tree_comp_dom"
                :treeData="treeDataDict"
                @clickCallback="clickCallback"
              ></treeCompRadio>
            </div>
          </div>
          <div class="main_left_choose page_area">
            <p class="choose_testee_title flex_row_between">
              <span>被评人员</span>
            </p>
            <div class="filter_bar">
              <div class="filter_item">
                <div class="item_label">是否被选</div>
                <div class="item_content">
                  <el-select v-model="isChecked" clearable placeholder="请选择">
                    <el-option
                      v-for="item in checkOptions"
                      :key="item.dictCode"
                      :label="item.codeName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
              <div class="filter_item">
                <div class="item_label">姓名</div>
                <div class="item_content">
                  <el-input v-model="memberName" placeholder=""></el-input>
                </div>
              </div>
              <div class="filter_item">
                <el-button
                  class="page_add_btn"
                  type="primary"
                  @click="searchList"
                  >查询</el-button
                >
              </div>
              <div class="filter_item" v-if="isEdit" style="margin-left: auto">
                <el-button
                  class="page_add_btn"
                  type="primary"
                  @click="importStaffDialog = true"
                  >导入数据</el-button
                >
              </div>
            </div>
            <tableComponent
              :tableData="tableData"
              :needIndex="true"
              :selectionStatus="true"
              height="400"
              :checkSelection="checkedList"
              @selectionChange="selectionChange"
              @curSelectInfo="curSelectInfo"
              @selectAll="selectAll"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
            ></tableComponent>
          </div>
        </div>
      </div>
      <div class="main_right" v-if="!editStaff">
        <ul class="from_wrap page_area">
          <li>
            <p>平均对多少词典进行评价(项)</p>
            <div>
              <el-input
                type="number"
                min="0"
                v-model="avgItemCount"
                size="mini"
              ></el-input>
            </div>
          </li>
          <li>
            <p>单人维护时间预估(分钟)</p>
            <div>
              <el-input
                type="number"
                min="0"
                v-model="avgEvalTime"
                size="mini"
              ></el-input>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="talent_raview_btn_wrap align_center marginT_30" v-if="isEdit">
      <el-button
        class="page_confirm_btn"
        v-if="!editStaff"
        type="primary"
        @click="prev()"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="next()"
        >下一步</el-button
      >
    </div>
    <!-- 导入弹窗 -->
    <el-dialog
      title="开始导入"
      v-model="importStaffDialog"
      width="800px"
      :before-close="importStaffDialogBeforeClose"
    >
      <div class="import_staff_wrap">
        <div class="import_staff_title">操作步骤:</div>
        <div class="oper_step">
          <p>1、下载《参与人员导入模板》</p>
          <p>2、打开下载表，标记需要参与本次测评的人员。</p>
          <p>3、信息输入完毕，点击“选择文件”按钮，选择excel文档。</p>
          <p>4、点击"开始导入",导入中如有任何疑问，请致电010-86482868。</p>
        </div>
        <div class="import_staff_title">填写须知:</div>
        <div class="oper_step">
          <p>1、不能在该Excel表中对员工信息类别进行增加、删除或修改；</p>
          <p>2、Excel中红色字段为必填字段，黑色字段为选填字段；</p>
          <p>3、参与测评人员请标记Y</p>
        </div>
        <div
          class="fs16 main_color pointer download_file"
          @click="exportSheetFn"
        >
          立即下载《参与人才测评导入模板》
        </div>
        <div class="upload_file_wrap">
          <el-input placeholder="请输入内容" v-model="fileName" readonly>
            <template v-slot:append>
              <label for="up" class="upload_label">
                选择文件
                <!-- <el-button type="primary">选择文件</el-button> -->
                <input
                  id="up"
                  style="display: none"
                  ref="file"
                  type="file"
                  class="form-control page_clear_btn"
                  @change="fileChange"
                />
              </label>
            </template>
          </el-input>
        </div>
        <!-- <div class="import_staff_title">当手机号重复时:</div>
                <div class="marginT_16">
                    <el-radio-group v-model="phoneType">
                        <el-radio label="Y">覆盖更新</el-radio>
                        <el-radio label="N">不导入</el-radio>
                    </el-radio-group>
                </div>-->
      </div>
      <template v-slot:footer>
        <div>
          <el-button class="page_clear_btn" @click="importStaffDialog = false"
            >取 消</el-button
          >
          <el-button type="primary" class="page_add_btn" @click="importEvalObj"
            >开始导入</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getOrgDeptTree } from "@/views/entp/request/api";
import {
  getEvalPost,
  setUpEvalUserObject,
  exportSheet,
  updateEvalInfo,
  getEvalInfo,
  importEvalPostObject,
  getDictItem,
} from "../../../request/api";
import treeCompRadio from "@/components/talent/treeComps/treeCompRadio";
import tabsChangeData from "@/components/talent/tabsComps/tabsChangeData";
import tableComponent from "@/components/talent/tableComps/tableComponent";
import { useUserStore } from "@/stores/modules/user";
export default {
  name: "chooseEvalObj",
  components: {
    treeCompRadio,
    tabsChangeData,
    tableComponent,
  },
  props: {
    evalId: {
      type: String,
      default: 0,
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
    // 是否是修改参与人员展示页面
    editStaff: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      exportFlag: true,
      treeDataDict: [],
      avgItemCount: "",
      avgEvalTime: "",
      checkOrgCode: "",
      importStaffDialog: false,
      fileName: "",
      current: 1,
      size: 10,
      checkOptions: [],
      isChecked: this.isEdit ? "" : "Y",
      memberName: "",
      checkedList: [],
      tableData: {
        columns: [
          {
            label: "员工姓名",
            prop: "userName",
            fixed: true,
          },
          {
            label: "一级组织",
            prop: "oneLevelName",
          },
          {
            label: "二级组织",
            prop: "twoLevelName",
          },
          {
            label: "三级组织",
            prop: "threeLevelName",
          },
          {
            label: "四级组织",
            prop: "fourLevelName",
          },
          {
            label: "五级组织",
            prop: "fiveLevelName",
          },
          {
            label: "任职岗位",
            prop: "postName",
          },
          {
            label: "职层",
            prop: "jobLevelName",
          },
          {
            label: "是否被评",
            prop: "flag",
            formatterFun: function (data) {
              return data.flag == "Y" ? "是" : "否";
            },
          },
        ],
        data: [],
        page: {
          current: 1,
          size: 10,
          total: 0,
        },
      },
    };
  },
  created() {
    // this.getEvalPostFun();
    this.getOrgDeptTreeFun();
    this.getEvalInfoFun();
    // this.$getDocList(["YES_NO"]).then((res) => {
    //   console.log(res);
    //   this.checkOptions = res.YES_NO;
    // });
    getDictItem({
      dictId: "YES_NO",
    }).then((res) => {
      console.log(res);
      if (res.code == 200) {
        // res = res.data;
        this.checkOptions = res.data;
      }
    });
  },

  // const companyId = computed(() => userStore.userInfo.companyId);
  computed: {
    companyId() {
      let userStore = useUserStore();
      return userStore.userInfo.companyId;
    },
  },
  methods: {
    getEvalInfoFun() {
      getEvalInfo({
        evalId: this.evalId,
      }).then((res) => {
        this.avgItemCount = res.avgItemCount;
        this.avgEvalTime = res.avgEvalTime;
      });
    },
    getOrgDeptTreeFun() {
      getOrgDeptTree({ companyId: this.companyId }).then((res) => {
        console.log(res);
        this.treeDataDict = res.data;
        this.checkOrgCode = this.treeDataDict[0].code;
        this.getEvalPostFun();
      });
    },
    searchList() {
      this.current = 1;
      this.getEvalPostFun();
    },
    getEvalPostFun() {
      getEvalPost({
        evalId: this.evalId,
        orgCode: this.checkOrgCode,
        current: this.current,
        size: this.size,
        userName: this.memberName,
        flag: this.isChecked,
      }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.checkedList = [];
          this.tableData.data = res.data;
          this.tableData.page.total = res.total;
          // this.$set(this.tableData, "data", res.data);
          // this.$set(this.tableData, "page", res.page);
          this.tableData.data.forEach((item) => {
            if (item.flag == "Y") {
              this.checkedList.push(item);
            }
          });
        }
      });
    },
    clickCallback(val) {
      console.log(val);
      this.checkOrgCode = val;
      this.getEvalPostFun();
    },
    // 复选框change回调
    selectionChange(data) {
      // console.log("复选框change回调");
      // console.log(data);
    },
    // 复选框点击回调
    curSelectInfo(state, info) {
      console.log("复选框点击回调");
      console.log(state);
      console.log(info);
      let params = {
        evalId: this.evalId,
        objPostCode: info.postCode,
        objectId: info.userId,
        flag: info.flag == "Y" ? "N" : "Y",
      };

      if (this.editStaff) {
        this.$confirm(info.flag == "Y" ? "确认移除人员?" : "确认添加", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.setUpEvalUserObjectFun([params]);
          })
          .catch(() => {
            console.log("取消");
            params.flag = info.flag;
            this.setUpEvalUserObjectFun([params]);
          });
      } else {
        this.setUpEvalUserObjectFun([params]);
      }
    },
    setUpEvalUserObjectFun(params) {
      setUpEvalUserObject(params).then((res) => {
        console.log(res);
        if (res.code == 200) {
          // this.$emit("nextStep");
          this.getEvalPostFun();
        } else {
          ElMessage.warning(res.msg);
        }
      });
    },
    // 勾选全选
    selectAll(selection) {
      console.log(selection);
      let list = null;
      let flag = "Y";
      let arr = [];
      if (selection.length > 0) {
        list = selection;
      } else {
        flag = "N";
        list = this.$util.deepClone(this.tableData.data);
      }

      list.forEach((item) => {
        arr.push({
          evalId: this.evalId,
          objPostCode: item.postCode,
          objectId: item.userId,
          flag: flag,
        });
      });

      if (this.editStaff) {
        this.$confirm(
          selection.length > 0 ? "确认添加列表人员" : "确认移除当前列表人员？",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            this.setUpEvalUserObjectFun(arr);
          })
          .catch(() => {
            console.log("取消");
            list.forEach((item) => {
              arr.push({
                evalId: this.evalId,
                objPostCode: item.postCode,
                objectId: item.userId,
                flag: item.flag,
              });
            });
            this.setUpEvalUserObjectFun(arr);
          });
      } else {
        this.setUpEvalUserObjectFun(arr);
      }

      // this.setUpEvalUserObjectFun(arr);
    },
    handleSizeChange(size) {
      this.size = size;
      this.tableData.page.size = size;
      this.getEvalPostFun();
    },
    handleCurrentChange(current) {
      this.current = current;
      this.tableData.page.current = current;
      this.getEvalPostFun();
    },
    exportSheetFn() {
      if (!this.exportFlag) return;
      this.exportFlag = false;
      exportSheet({ evalId: this.evalId }).then((res) => {
        this.exportFlag = true;
        console.log(res);
        const blob = new Blob([res]);
        const elink = document.createElement("a");
        elink.download = "参与人才测评导入模板.xlsx";
        elink.style.display = "none";
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink);
      });
    },
    //导入
    importStaffDialogBeforeClose(done) {
      this.fileName = "";
      this.uploadFile = null;
      document.getElementById("up").value = null;
      done();
    },
    fileChange(e) {
      let formData = new FormData();
      //把文件信息放入对象中
      let file = e.target.files[0];
      //把文件信息放入对象中
      formData.append("file", file);
      formData.append("evalId", this.evalId);
      this.fileName = file.name;
      this.uploadFile = formData;
    },
    importEvalObj() {
      importEvalPostObject(this.uploadFile).then((res) => {
        if (res.code == "200") {
          ElMessage.success(res.msg);
          this.uploadFile = null;
          this.fileName = "";
          this.importStaffDialog = false;
          this.current = 1;
          this.getEvalPostFun();
        } else {
          this.uploadFile = null;
          this.fileName = "";
          ElMessage.error("模板文件格式不正确，请重新下载模板文件");
        }
      });
    },
    prev: function () {
      this.$emit("prevStep");
    },
    async next() {
      let res = await getEvalPost({
        evalId: this.evalId,
        orgCode: this.checkOrgCode,
        current: 1,
        size: this.size,
        flag: "Y",
      });
      console.log(res);
      if (res.data == null) {
        ElMessage.warning("请选择被评人员");
        return;
      }

      if (!this.avgEvalTime) {
        ElMessage.warning("请填写“单人维护时间预估(分钟)”");
        return;
      }
      if (!this.avgItemCount) {
        ElMessage.warning("请填写“平均对多少词典进行评价(项)”");
        return;
      }
      let params = {
        avgEvalTime: this.avgEvalTime,
        avgItemCount: this.avgItemCount,
        evalId: this.evalId,
      };
      updateEvalInfo(params).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.$emit("nextStep");
        } else {
          ElMessage.error(res.msg);
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.filter_bar {
  padding: 8px 16px;
  display: flex;
  .filter_item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    .item_label {
      margin-right: 4px;
    }
    .item_content {
      :deep .el-select {
        width: 180px;
      }
    }
  }
}
.upload_wrap {
  position: relative;
  width: 80px;
  height: 30px;
  margin-left: 10px;
  cursor: pointer;
}
.import_btn {
  font-size: 0;
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}
// 导入
.import_staff_wrap {
  .import_staff_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
  }

  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}
</style>
<style scoped lang="scss">
.choose_testee_wrap {
  &.disable {
    .from_wrap {
      pointer-events: none;
    }
    ::v-deep .el-table {
      pointer-events: none;
    }
  }
  .choose_testee_title {
    padding: 0 8px;
    font-size: 16px;
    height: 34px;
    line-height: 34px;
    background: #ebf4ff;
    .remove_icon {
      font-size: 14px;
      color: #f00;
    }
    .remove_all {
      cursor: pointer;
      line-height: 34px;
      color: #f00;
      font-size: 14px;
    }
    .icon_check_all {
      display: inline-block;
      border: 2px solid #666;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      font-size: 18px;
      font-weight: bold;
      color: #e5f0f9;
      margin-left: 5px;
      line-height: 22px;
      text-align: center;
      margin-right: -5px;
      &.active {
        color: #0099ff;
        border-color: #0099ff;
      }
    }
  }

  .page_area {
    height: 550px;
    overflow: auto;
    border: 1px solid #e5e5e5;
  }

  .main_left_title {
    padding: 3px 8px;
    font-size: 16px;
    line-height: 40px;
    background: #ebf4ff;
  }

  .choose_testee_main {
    .second_level_post_wrap {
      height: 500px;
      padding: 10px;
      overflow-y: auto;
      .tree_comp_dom {
        width: 300px;
      }

      li {
        position: relative;
        margin: 5px 0 0 0;
        padding: 0 5px;
        height: 36px;
        line-height: 36px;
        cursor: pointer;
        border: 1px solid #e4e4e4;

        .icon_check {
          position: absolute;
          border: 1px solid #ddd;
          border-radius: 50%;
          right: 5px;
          top: 5px;
          width: 24px;
          height: 24px;
        }

        .el-icon-check {
          height: 35px;
          font-size: 24px;
          line-height: 35px;
          font-weight: bold;
        }

        .el_del_bg {
          position: absolute;
          right: 5px;
          top: 5px;
          width: 24px;
          height: 24px;
          background: #ddd;
          border-radius: 50%;
          line-height: 24px;
          font-size: 20px;
          color: #fff;
          text-align: center;
        }

        .el-icon-remove-outline {
          height: 35px;
          font-size: 24px;
          line-height: 35px;
          color: #ccc;
        }
      }

      .active {
        border: 1px solid #0099ff;
        color: #0099ff;
      }

      .hover_style:hover {
        background: #ebf4ff;
      }
    }

    .main_left {
      // width:512px;
      width: 50%;
      flex: 1 1 0;
      margin-right: 20px;
      .main_left_tree {
        flex: 0 0 200px;
        // width: 40%;
        margin-right: 20px;
        overflow: auto;
      }
      .main_left_choose {
        flex: 1;
        // width: 164px;
      }
    }

    .main_right {
      width: 200px;
      // width: 50%;
      .from_wrap {
        // width: 182px;
        padding: 10px;
        margin-top: 20px;
        li {
          p {
            height: 35px;
            line-height: 35px;
          }

          div {
            .el-input__inner {
              width: 100%;
              height: 35px;
            }
          }
        }
      }
    }
  }
}
</style>
