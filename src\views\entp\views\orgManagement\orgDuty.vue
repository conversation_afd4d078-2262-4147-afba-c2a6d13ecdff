<template>
  <div class="org_duty_wrap bg_write">
    <div class="page_main_title">组织职责</div>
    <div class="page_section">
      <div class="org_duty_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">业务领域</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              :treeData="treeData"
              :needCheckedFirstNode="false"
              :canCancel="true"
              @clickCallback="clickCallback"
            />
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">筛选</div>
              <div class="filter_item">
                <el-input v-model="respName" placeholder="按名称查询">
                  <template #suffix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="keyWordSearch"> 查询 </el-button>
              </div>
            </div>

            <div class="filter_item">
              <el-button class="page_add_btn" type="primary" @click="creatOrgDuty"> 新增 </el-button>
              <el-button
                :class="{ froBid_import: hasOrgDutySign, page_add_btn: true }"
                type="primary"
                @click="orgDutyImport"
              >
                导入
              </el-button>
              <el-button class="page_add_btn" type="primary" @click="orgDutyExport"> 导出 </el-button>
            </div>
          </div>
          <table-component
            :tableData="tableData"
            :needIndex="needIndex"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          >
            <template #oper>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button
                    @click="tableEdit(scope.$index, tableData.data)"
                    type="primary"
                    :icon="Edit"
                    class="icon_edit"
                    text
                  />
                  <el-button
                    class="color_danger icon_del"
                    @click="tableDeleteRow(scope.$index, tableData.data)"
                    type="danger"
                    :icon="Delete"
                    text
                  />
                </template>
              </el-table-column>
            </template>
          </table-component>
        </div>
      </div>
    </div>
    <orgDutyPopUp
      v-model:show="show"
      :popupTitleSign="popupTitleSign"
      :editRespCode="editRespCode"
      @updateSuccessSign="updateSuccessSign"
    />
    <org-duty-import-popUp v-model:show="showPopUp" @importSign="importSign" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Edit, Delete } from '@element-plus/icons-vue'
import { getDomainList, getRespList, deleteResp, exportOrgDuty, exportDownload } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import tableComponent from '@/components/talent/tableComps/tableComponent.vue'
import orgDutyPopUp from '../tPopUpComps/orgDutyPopUp'
import orgDutyImportPopUp from '../tPopUpComps/orgDutyImportPopUp'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// 响应式状态
const needIndex = ref(true)
const rStatus = ref('')
const checkBizDomain = ref('')
const respName = ref('')
const treeData = ref([])
const show = ref(false)
const popupTitleSign = ref(true)
const editRespCode = ref('')
const showPopUp = ref(false)
const hasOrgDutySign = ref(false)

// 表格数据
const tableData = reactive({
  columns: [
    {
      label: '职责编码',
      prop: 'respCode'
    },
    {
      label: '职责名称',
      prop: 'respName'
    },
    {
      label: '业务领域',
      prop: 'bizDomainName'
    },
    {
      label: '职责描述',
      prop: 'respDesc'
    }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})

// 方法
const getDomainListFun = async () => {
  try {
    const res = await getDomainList({
      companyId: companyId.value,
      rStatus: rStatus.value
    })

    if (res.code == 200) {
      treeData.value =
        res.data.length > 0
          ? res.data.map(item => ({
              code: item.bizDomainCode,
              value: item.bizDomainName
            }))
          : []
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error('获取业务领域列表失败:', error)
    treeData.value = []
  }
}

const clickCallback = val => {
  checkBizDomain.value = val
  tableData.page.current = 1
  getRespListFun()
}

const getRespListFun = async val => {
  try {
    const res = await getRespList({
      bizDomainCode: checkBizDomain.value,
      respName: respName.value,
      current: tableData.page.current,
      size: tableData.page.size
    })

    tableData.data = []

    if (res.code == 200 && res.data) {
      if (val == 'hasOrgDutySign') {
        hasOrgDutySign.value = res.total > 0
      }

      tableData.data = res.data
      tableData.page.total = res.total
    } else {
      tableData.page = {
        total: 0,
        current: 1,
        size: 10
      }
    }
  } catch (error) {
    console.error('获取职责列表失败:', error)
    tableData.data = []
    tableData.page = {
      total: 0,
      current: 1,
      size: 10
    }
  }
}

const handleSizeChange = val => {
  tableData.page.size = val
  getRespListFun()
}

const handleCurrentChange = val => {
  tableData.page.current = val
  getRespListFun()
}

const keyWordSearch = () => {
  tableData.page.current = 1
  getRespListFun()
}

const creatOrgDuty = () => {
  show.value = true
  popupTitleSign.value = true
}

const updateSuccessSign = updateSuccessSign => {
  if (updateSuccessSign) {
    if (!popupTitleSign.value) {
      tableData.page.current = 1
    }
    getRespListFun()
  }
}

const tableEdit = (index, data) => {
  editRespCode.value = data[index].respCode
  popupTitleSign.value = false
  show.value = true
}

const tableDeleteRow = async (index, data) => {
  try {
    await ElMessageBox.confirm('确定删除?', '确定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    await deleteRespFun(data[index].respCode)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除职责失败:', error)
    }
  }
}

const deleteRespFun = async val => {
  try {
    const res = await deleteResp({
      respCode: val
    })

    if (res.code == 200) {
      tableData.page.current = 1
      getRespListFun()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('删除职责失败:', error)
    ElMessage.error('删除职责失败')
  }
}

const orgDutyImport = () => {
  if (hasOrgDutySign.value) {
    ElMessage.warning('企业无组织职责时才有导入功能!')
    return
  }
  showPopUp.value = true
}

const orgDutyExport = () => {
  exportOrgDutyFun()
}

const exportOrgDutyFun = async () => {
  try {
    const res = await exportOrgDuty({
      bizDomainCode: checkBizDomain.value
    })

    if (res.code == 200) {
      await exportDownloadFun(res.data)
    } else {
      ElMessage.warning(res.msg)
    }
  } catch (error) {
    console.error('导出职责失败:', error)
    ElMessage.error('导出职责失败')
  }
}

const exportDownloadFun = async val => {
  try {
    const res = await exportDownload({
      fileName: val
    })

    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '组织职责列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

const importSign = importSign => {
  if (importSign) {
    getRespListFun('hasOrgDutySign')
  }
}

// 监听公司ID变化
watch(
  () => companyId.value,
  val => {
    if (val) {
      getDomainListFun()
    }
  },
  { immediate: true }
)

// 初始化
getRespListFun('hasOrgDutySign')
</script>

<style scoped>
.org_duty_wrap .aside_tree_list li {
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}

.org_duty_wrap .aside_tree_list li span {
  display: inline-block;
  width: 14px;
}

.org_duty_wrap .aside_tree_list li:hover {
  color: var(--el-color-primary);
}

.org_duty_wrap .aside_tree_list .li_act {
  color: var(--el-color-primary);
}

.org_duty_wrap .froBid_import {
  background: var(--el-disabled-bg-color);
  border: 1px solid var(--el-disabled-border-color);
  color: var(--el-text-color-placeholder);
}

.icon_edit {
  margin-right: 8px;
}

.icon_edit,
.icon_del {
  font-size: 16px;
}
</style>
