<template>
  <el-dialog
    :title="popupTitleSign ? '新增组织' : '修改组织'"
    v-model="dialogVisible"
    @close="$emit('update:show', false)"
    width="40%"
    center
  >
    <div class="line_wrap flex_row_betweens">
      <span>上级组织：</span>
      <div>
        <el-cascader
          :options="treeData"
          v-model="parentOrgCode"
          :placeholder="popupTitleSign ? '请选择' : parentOrgCode ? '' : '尚无上级组织'"
          :change-on-select="true"
          :props="{
            label: 'value',
            value: 'code',
            expandTrigger: 'hover'
          }"
          @change="handleItemChange"
          :key="cascaderKey"
          :disabled="!popupTitleSign"
          clearable
        >
        </el-cascader>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>业务领域：</span>
      <div>
        <el-select v-model="bizDomainCode" placeholder="请选择" clearable>
          <el-option
            v-for="opt in bizDomainOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span><span class="required_fields_icon">&lowast;</span>是否实体：</span>
      <div>
        <el-select v-model="isEntity" clearable placeholder="请选择">
          <el-option
            v-for="opt in isEntityOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens" v-if="!popupTitleSign">
      <span>组织负责人：</span>
      <div>
        <el-select
          v-model="orgLeaderName"
          filterable
          remote
          :remote-method="getCandidateFun"
          @change="candidateChange"
          :loading="loading"
          clearable
          placeholder="请输入人员名称进行查询"
        >
          <el-option v-for="opt in staffOptions" :key="opt.dictCode" :label="opt.codeName" :value="opt.dictCode">
            <span class="options_item">{{ opt.codeName }}</span>
            -- <span class="options_item">{{ opt.orgName }}</span> --
            <span class="options_item">{{ opt.postName }}</span>
          </el-option>
        </el-select>
      </div>
    </div>

    <div class="line_wrap flex_row_betweens">
      <span>组织层级：</span>
      <div>
        <el-select v-model="orgLevelCode" clearable placeholder="请选择">
          <el-option
            v-for="opt in orgLevelOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>组织名称：</span>
      <div>
        <el-input v-model="orgName"></el-input>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens" v-if="!popupTitleSign">
      <span>是否启用：</span>
      <div>
        <el-switch
          v-model="switchStatus"
          :disabled="disabledRstatus"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="是"
          inactive-text="否"
        >
        </el-switch>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>组织简称：</span>
      <div>
        <el-input v-model="orgShortName"></el-input>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span><span class="required_fields_icon">&lowast;</span>组织外部编码：</span>
      <div>
        <el-input v-model="orgCodeExtn" maxlength="32"></el-input>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer align_right">
        <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
        <el-button class="page_add_btn" type="primary" @click="submitBtn">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  getOrgDeptTree,
  getOrgLevelList,
  getOrgStaffList,
  principal,
  createOrgDept,
  updateOrgDept,
  getOrgDeptInfo,
  getDomainList
} from '../../request/api'
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// Props定义
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  //popupTitleSign 为true时新增组织，非true时修改组织 ，新增/修改组织的唯一标识
  popupTitleSign: Boolean,
  checkedId: String, //组织树带过来的orgCode
  tebleEditId: String, //组织列表带过来的 orgCode
  isDeleteSign: Boolean
})

// Emits定义
const emit = defineEmits(['update:show'])

// 响应式状态
const cascaderKey = ref(1)
const dialogVisible = computed({
  get: () => props.show,
  set: value => emit('update:show', value)
})
const treeData = ref([])
const rStatus = ref('')
const switchStatus = ref(true)
const disabledRstatus = ref(false)
const orgLevelOptions = ref([])
const likeName = ref('')
const bizDomainOptions = ref([])
const isEntityOptions = ref([])
const staffOptions = ref([])
const loading = ref(false)

// 表单数据
const parentOrgCode = ref('')
const bizDomainCode = ref('')
const isEntity = ref('')
const orgLeaderName = ref('')
const orgLevelCode = ref('')
const orgName = ref('')
const orgShortName = ref('')
const orgCodeExtn = ref('')

// 方法定义
const handleItemChange = async value => {
  // 实现级联选择器change事件处理
}

const getCandidateFun = async query => {
  if (query !== '') {
    loading.value = true
    try {
      const res = await principal({ userName: query })
      staffOptions.value = res.data || []
    } catch (error) {
      console.error('获取员工列表失败:', error)
    } finally {
      loading.value = false
    }
  } else {
    staffOptions.value = []
  }
}

const candidateChange = value => {
  // 实现候选人选择变更处理
}

const cancel = () => {
  dialogVisible.value = false
}

const submitBtn = async () => {
  try {
    const formData = {
      parentOrgCode: parentOrgCode.value,
      bizDomainCode: bizDomainCode.value,
      isEntity: isEntity.value,
      orgLeaderName: orgLeaderName.value,
      orgLevelCode: orgLevelCode.value,
      orgName: orgName.value,
      switchStatus: switchStatus.value,
      orgShortName: orgShortName.value,
      orgCodeExtn: orgCodeExtn.value
    }

    if (props.popupTitleSign) {
      await createOrgDept(formData)
    } else {
      await updateOrgDept(formData)
    }

    dialogVisible.value = false
    // 可以添加成功提示
    ElMessage.success(props.popupTitleSign ? '新增成功' : '修改成功')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

// 初始化数据
const initData = async () => {
  try {
    const res = await Promise.all([
      getOrgDeptTree(),
      getOrgLevelList(),
      getDomainList({ companyId: companyId.value }),
      // 假设有获取实体选项的API
      Promise.resolve({
        data: [
          { dictCode: '1', codeName: '是' },
          { dictCode: '0', codeName: '否' }
        ]
      })
    ])

    console.log('resresresres', res)

    let [treeRes, levelRes, domainRes, entityRes] = res

    treeData.value = treeRes.data || []
    orgLevelOptions.value = levelRes.data || []
    bizDomainOptions.value = domainRes.data || []
    isEntityOptions.value = entityRes.data || []

    if (!props.popupTitleSign && props.tebleEditId) {
      const detailRes = await getOrgDeptInfo({ orgCode: props.tebleEditId })
      const detail = detailRes.data

      // 填充表单数据
      parentOrgCode.value = detail.parentOrgCode
      bizDomainCode.value = detail.bizDomainCode
      isEntity.value = detail.isEntity
      orgLeaderName.value = detail.orgLeaderName
      orgLevelCode.value = detail.orgLevelCode
      orgName.value = detail.orgName
      switchStatus.value = detail.switchStatus
      orgShortName.value = detail.orgShortName
      orgCodeExtn.value = detail.orgCodeExtn
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('加载数据失败，请刷新重试')
  }
}

// 生命周期钩子
onMounted(() => {
  initData()
})

// 监听props变化
watch(
  () => props.show,
  newVal => {
    if (newVal) {
      initData()
    }
  }
)
</script>

<style scoped>
.line_wrap {
  margin-bottom: 20px;
}
.flex_row_betweens {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.required_fields_icon {
  color: red;
  margin-right: 4px;
}
.options_item {
  margin: 0 4px;
}
.align_right {
  text-align: right;
}
</style>
