<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in eduInfoData" :key="item.educationId">
      <el-input class="item school_name" v-model.trim="item.graduateSchool" placeholder="填写院校名称"></el-input>
      <el-date-picker
        class="item"
        value-format="YYYY-MM-DD"
        v-model="item.graduateDate"
        type="date"
        placeholder="选择日期"
      ></el-date-picker>
      <el-select class="item" v-model="item.qualification" placeholder>
        <el-option
          v-for="item in qualificationOptions"
          :key="item.dictCode"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.highestQualification" placeholder>
        <el-option label="是" value="Y"></el-option>
        <el-option label="否" value="N"></el-option>
      </el-select>
      <el-select class="item" v-model="item.postRelated" placeholder>
        <el-option
          v-for="item in yesOrNo"
          :label="item.codeName"
          :key="item.dictCode"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.industryRelated" placeholder>
        <el-option
          v-for="item in yesOrNo"
          :label="item.codeName"
          :key="item.dictCode"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <div class="item item_icon_wrap">
        <i class="item_icon el-icon-delete" @click="deleteItem(item, index)"></i>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  eduInfoData: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['deleteItem'])

const qualificationOptions = ref([])
const yesOrNo = ref([])

const pickerOptions2 = {
  disabledDate(time) {
    return time.getTime() > Date.now()
  }
}

// 获取字典方法，优先用 inject，否则用 props 传递
const getDocList = inject('getDocList', null)

onMounted(async () => {
  let res = null
  if (getDocList) {
    res = await getDocList(['QUALIFICATION', 'YES_NO'])
  } else if (typeof window.$getDocList == 'function') {
    res = await window.$getDocList(['QUALIFICATION', 'YES_NO'])
  }
  if (res) {
    qualificationOptions.value = res.QUALIFICATION
    yesOrNo.value = res.YES_NO
  }
})

function deleteItem(item, index) {
  emit('deleteItem', item, index)
}
</script>

<style scoped lang="scss">
.edu_info_item {
  .item {
    // float: 1;
    width: 23%;
    padding: 0 4px;
    line-height: 30px;

    &.school_name {
      width: 27%;
    }
  }

  .item_icon_wrap {
    text-align: center;
    margin-top: 3px;
    width: 5%;

    .item_icon {
      font-size: 20px;
      color: #f56c6c;
      cursor: pointer;
    }
  }
}
</style>
