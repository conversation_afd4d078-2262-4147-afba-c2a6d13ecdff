<template>
  <div class="assess_progress_manage_info_wrap bg_write" v-if="showPage">
    <div class="page_main_title">
      盘点评价进度--{{ enqName }}
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="page_second_title">
        部门盘点人员
        <el-button type="primary" class="page_add_btn fr" @click="exportExcel">导出</el-button>
      </div>
      <!-- <tabsChangeData
                    :tabsData="tabsData"
                    :activeName="tabsData[tabIndex].name"
                     :handleClick="changeTabs">
            </tabsChangeData> -->
      <!-- <div class="department_level_wrap">
                <span class="select_title">部门：</span>
                <el-cascader
                    class="filter_item"
                    :options="orgTree"
                    v-model="orgCode"
                    placeholder="请选择"
                    :change-on-select="true"
                    :show-all-levels="false"
                    :props="{
                        label: 'name',
                        value: 'code',
                        expandTrigger: 'hover',
                    }"
                    @change="(val) => orgTreeClick(val)"
                    clearable
                >
                </el-cascader>
                <span class="select_title">人员：</span>
                <el-input
                    class="filter_item"
                    v-model="userName"
                    placeholder="请输入人员名称"
                ></el-input>
                <span class="select_title">状态：</span>
                <el-select
                    class="filter_item marginR_16"
                    v-model="status"
                    clearable
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in statusOption"
                        :key="item.dictCode"
                        :label="item.codeName"
                        :value="item.dictCode"
                    >
                    </el-option>
                </el-select>
                <el-button
                    @click="searchFun"
                    type="primary"
                    
                    >查询</el-button
                >
                <el-button
                    @click="exportExcel"
                    type="primary"
                    
                    >导出</el-button
                >
            </div> -->
      <tableComponent
        v-if="tableData.data.length > 0"
        :tableData="tableData"
        :needIndex="true"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      >
      </tableComponent>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tabsChangeData from '@/components/talent/tabsComps/tabsChangeData'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import { orgUserEvaluate, userReturn, userInvalid, getEvalInfo, scheduleTree, orgExportData } from '../../request/api'
import { exportDownload } from '@/views/entp/request/api'

const route = useRoute()
const showPage = ref(true)
const enqId = route.query.enqId
const enqName = route.query.enqName
const evalStatus = ref(null)
const orgTree = ref([])
const orgCode = ref(null)
const userName = ref('')
const status = ref('')
const statusOption = ref([])
const pageCurrent = ref(1)
const pageSize = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '一级部门',
      prop: 'oneLevelName'
    },
    {
      label: '二级部门',
      prop: 'twoLevelName'
    },
    {
      label: '三级部门',
      prop: 'threeLevelName'
    },
    {
      label: '四级部门',
      prop: 'fourLevelName'
    },
    {
      label: '部门',
      prop: 'orgName'
    },
    {
      label: '岗位',
      prop: 'postName',
      className: 'align_center'
    },
    {
      label: '姓名',
      prop: 'userName',
      width: 80,
      className: 'align_center'
    },
    {
      label: '评价人',
      prop: 'evaluatorName',
      width: 80,
      className: 'align_center'
    },
    {
      label: '状态',
      prop: 'confirmStatus',
      width: 70,
      className: 'align_center'
    }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})

//切换页容量
const handleSizeChange = val => {
  pageSize.value = val
  orgUserEvaluateFun()
}

//分页
const handleCurrentChange = val => {
  pageCurrent.value = val
  orgUserEvaluateFun()
}

const orgUserEvaluateFun = async () => {
  const res = await orgUserEvaluate({
    enqId: enqId,
    current: pageCurrent.value,
    size: pageSize.value
  })
  if (res.code == 200) {
    tableData.data = res.data
    tableData.page = res.page
  } else {
    ElMessage.warning(res.msg)
  }
}

const exportExcel = async () => {
  const params = {
    enqId: enqId,
    type: 'c'
  }
  const res = await orgExportData(params)
  if (res.code == 200) {
    exportDownloadFun(res.data)
  } else {
    ElMessage.warning(res.msg)
  }
}

const exportDownloadFun = async val => {
  const res = await exportDownload({
    fileName: val
  })
  const blob = new Blob([res])
  const elink = document.createElement('a')
  elink.download = enqName + '--测评进度.xlsx'
  elink.style.display = 'none'
  elink.href = URL.createObjectURL(blob)
  document.body.appendChild(elink)
  elink.click()
  URL.revokeObjectURL(elink.href) // 释放URL 对象
  document.body.removeChild(elink)
}

onMounted(() => {
  orgUserEvaluateFun()
})
</script>

<style scoped lang="scss">
.assess_progress_manage_info_wrap {
  .department_level_wrap {
    margin: 10px 0 20px 0;

    .select_title {
      display: inline-block;
      width: 60px;
      text-align: center;
    }
    .filter_item {
      width: auto;
    }

     .el-input__inner {
      height: 35px;
      line-height: 35px;
    }

     .el-input__suffix {
      display: flex;
      align-items: center;
    }
  }

   .el-table {
    margin: 10px 0 0 0;

    .has-gutter {
      tr th {
        background: #f2f8ff;
      }
    }

    .align_center {
      text-align: center;
      /*font-weight: bold;*/

      &.completed {
        color: #00b050;
      }

      &.incomplete {
        color: #ffc000;
      }

      &.not_login {
        color: #00b0f0;
      }

      &.status {
      }
    }
  }

   .el-table__body .last_date {
    font-size: 12px;
  }

  .completion_rate {
    .bar_wrap {
      width: calc(100% - 40px);
      height: 18px;
      background: #EBF4FF;
      position: relative;
      padding-top: 5px;

      .bar_progress {
        background: #00b050;
        height: 8px;
        width: 50%;

        &.bg_high {
          background: #00b050;
        }

        &.bg_middle {
          background: #00b0f0;
        }

        &.bg_normal {
          background: #ffc000;
        }

        &.bg_low {
          background: #ff8181;
        }
      }
    }

    .completion_rate_num {
      font-weight: bold;

      &.not_login {
        color: #ff6d6d;
      }

      &.color_high {
        color: #00b050;
      }

      &.color_middle {
        color: #00b0f0;
      }

      &.color_normal {
        color: #ffc000;
      }

      &.color_low {
        color: #ff8181;
      }
    }
  }
}
</style>
