import { defineStore } from 'pinia'
import { getUserBasicInfo, getCompanyInfo, getDictList } from '@/views/entp/request/api.js'
import { useRouter } from 'vue-router'

export const useUserStore = defineStore(
  'user',
  e => {
    console.log(e)

    const token = ref('')
    const userInfo = ref({})
    const createStaffId = ref(null)
    const companyInfo = ref({})
    const docList = ref({}) // 文档列表
    // 部分页面筛选条件缓存
    const cacheParams = reactive({
      // 员工管理
      staffMParams: {},
      // 岗位管理
      postMParams: {},
      // 职位管理
      jobMParams: {}
    })
    const setParams = data => {
      let { stateKey, params } = { ...data }
      cacheParams[stateKey] = { ...cacheParams[stateKey][stateKey], ...params }
    }
    const getParams = stateKey => {
      return cacheParams[stateKey]
    }

    const setUserInfo = info => {
      userInfo.value = info
    }

    const setToken = t => {
      token.value = t
    }
    const removeToken = () => {
      token.value = ''
    }

    const clearUserInfo = () => {
      userInfo.value = {}
      removeToken()
    }
    const getUserInfo = () => {
      getUserBasicInfo().then(res => {
        if (res.code == 200) {
          userInfo.value = res.data
        }
      })
    }
    const getCompanyInfo = () => {
      getCompanyInfo().then(res => {
        if (res.code == 200) {
          companyInfo.value = res.data
        }
      })
    }
    const router = useRouter()
    const logout = () => {
      clearUserInfo()
      router.push('/login')
    }

    const getDocList = dictIdArr => {
      let ids = dictIdArr.join(',')
      return getDictList({ dictIds: ids }).then(res => {
        if (res.code == 200) {
          docList.value = res.data
          return res.data
        }
      })
    }

    return {
      userInfo,
      token,
      getUserInfo,
      setUserInfo,
      setToken,
      removeToken,
      clearUserInfo,
      logout,
      createStaffId,
      companyInfo,
      cacheParams,
      getCompanyInfo,
      docList,
      getDocList,
      setParams,
      getParams
    }
  },
  {
    // 添加配置开启 state/ref 持久化存储
    // 插件默认存储全部 state/ref
    persist: {
      storage: window.sessionStorage
    }
  }
)
