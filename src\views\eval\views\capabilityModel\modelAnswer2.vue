<template>
  <div class="model_answer_wrap bg_write" id="answerPage" ref="answerPage">
    <div class="page_main_title clearfix">
      {{ pageTitle }}
      <div class="goback_geader" @click="goBackBtn"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="answer_progress_wrap">
        <div class="progress_text flex_row_between">
          <div>
            共{{ itemTotal }}题，已完成<span>{{ completedPercen }}%</span>
          </div>
          <div class="current_progress">
            第<span>{{ currItem }}</span
            >/{{ itemTotal }}题
          </div>
        </div>
        <div class="progress_bar">
          <div class="progress_tip" :style="{ left: progressPercen + '%' }">
            {{ currItem }}
            <i class="arrow_icon el-icon-caret-bottom"></i>
          </div>
          <el-progress
            :percentage="progressPercen"
            :stroke-width="6"
            :show-text="false"
            :color="'#0080e4'"
          ></el-progress>
        </div>
      </div>
      <div class="answer_surplus_wrap">
        <div class="assembly_name">
          请仔细阅读能力项：<span>{{ componentName }} </span>
          的详细描述，并对下列人员进行能力评价
        </div>
        <div class="assembly_desc">能力描述：{{ itemName }}</div>
        <!-- <div class="assembly_name">{{ itemName }}</div> -->
        <div class="assembly_desc">
          <pre>{{ itemDesc }}</pre>
        </div>
        <div class="answer_content">
          <table class="answer_table">
            <thead>
              <tr>
                <th class="index">序号</th>
                <!-- <th class="post">岗位</th> -->
                <th class="name">姓名</th>
                <th class="score">得分</th>
                <th class="answer_option">
                  <div class="option_item" v-for="item in answerOptions" :key="item.optionNbr">
                    {{ item.optionContent }}
                    <el-tooltip class="item" effect="dark" :content="item.optionDesc" placement="top">
                      <i class="icon el-icon-question"></i>
                    </el-tooltip>
                  </div>
                </th>
                <th rowspan="2" class="imp_emer importance">该能力对其发展的重要性</th>
                <th rowspan="2" class="imp_emer">该能力对其发展的紧急度</th>
              </tr>
              <tr class="score_tr">
                <th class="index"></th>
                <!-- <th class="post"></th> -->
                <th class="name"></th>
                <th class="score">得分</th>
                <th class="relative">
                  <div class="border_dom">
                    <td class="border" v-for="item in answerOptions" :key="item.optionNbr">.</td>
                  </div>
                  <div class="score_wrap">
                    <td>0</td>
                    <td>10</td>
                    <td>20</td>
                    <td>30</td>
                    <td>40</td>
                    <td>50</td>
                    <td>60</td>
                    <td>70</td>
                    <td>80</td>
                    <td>90</td>
                    <td>100</td>
                  </div>
                </th>
              </tr>
            </thead>
          </table>
          <div class="answer_table_wrap" ref="answerTbody">
            <table class="answer_table">
              <tbody>
                <tr v-for="(user, index) in userList" :key="user.objectId + '' + user.optionNbr">
                  <td class="index">{{ index + 1 }}</td>
                  <!-- <td class="post">{{ user.postName }}</td> -->
                  <td class="name">
                    {{ user.objectName }} <br />
                    {{ user.postName }}
                  </td>
                  <td class="score">
                    <div class="target_score_text" v-if="user.relationType == 'U'">目标</div>
                    <div>实际</div>
                  </td>
                  <td class="score_bar_box">
                    <div class="score_bar_wrap target" v-if="user.relationType == 'U'">
                      <el-slider
                        :step="answerOptions.length / 10"
                        :format-tooltip="formatTooltip"
                        :max="answerOptions.length"
                        v-model="user.expectedOptionNbr"
                      ></el-slider>
                    </div>
                    <div class="score_bar_wrap actual">
                      <el-slider
                        :step="answerOptions.length / 10"
                        :format-tooltip="formatTooltip"
                        :max="answerOptions.length"
                        v-model="user.optionNbr"
                      ></el-slider>
                    </div>
                  </td>
                  <td class="imp_emer importance">
                    <el-radio-group v-if="user.relationType == 'U'" v-model="user.importance">
                      <el-radio-button :label="item.dictCode" v-for="item in impOptions" :key="item.dictCode">{{
                        item.codeName
                      }}</el-radio-button>
                    </el-radio-group>
                    <!-- <el-select
                      v-if="user.relationType == 'U'"
                      v-model="user.importance"
                      placeholder="请选择重要度"
                    >
                      <el-option
                        v-for="item in impOptions"
                        :key="item.dictCode"
                        :label="item.codeName"
                        :value="item.dictCode"
                      >
                      </el-option>
                    </el-select> -->
                  </td>
                  <td class="imp_emer">
                    <el-radio-group v-if="user.relationType == 'U'" v-model="user.emergency">
                      <el-radio-button :label="item.dictCode" v-for="item in emerOptions" :key="item.dictCode">{{
                        item.codeName
                      }}</el-radio-button>
                    </el-radio-group>
                    <!-- <el-select
                      v-if="user.relationType == 'U'"
                      v-model="user.emergency"
                      placeholder="请选择紧急度"
                    >
                      <el-option
                        v-for="item in emerOptions"
                        :key="item.dictCode"
                        :label="item.codeName"
                        :value="item.dictCode"
                      >
                      </el-option>
                    </el-select> -->
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="align_center marginT_30">
        <el-button class="page_confirm_btn" v-if="preItemId != 0" type="primary" @click="prev()">上一题</el-button>
        <el-button class="page_confirm_btn" type="primary" @click="next()">下一题</el-button>
        <!-- <el-button class="page_confirm_btn" type="primary" @click="submit()"
          >提交</el-button> -->
      </div>
    </div>
  </div>
</template>

<script>
import { getItemInfo, getEvalInfo, saveEvalSubmit, answerSubmit ,getDictList,getDictItem} from '../../request/api'
export default {
  name: 'modelAnswer',
  data() {
    return {
      // 改版数据定义
      itemTotal: 60,
      currItem: 2,
      componentName: '',
      componentDesc: '',
      itemName: '',
      itemDesc: '',
      userList: [],
      answerOptions: [],
      emerOptions: [],
      impOptions: [],
      // 改版数据定义 end
      type: this.$route.query.type ? this.$route.query.type : '',
      lastBuildId: this.$route.query.lastBuildId ? this.$route.query.lastBuildId : this.$route.query.evalId,
      nextItemId: '',
      preItemId: '',
      itemId: '',
      // 参数部分
      currentItemId: null,
      pageTitle: '',
      startTime: '',
      submitStatus: true,

      userListFirstTarget: ''
    }
  },
  components: {},
  computed: {
    // 进度百分比
    progressPercen: function () {
      return Math.floor(((this.currItem / this.itemTotal) * 10000) / 100)
    },
    // 已完成的百分比
    completedPercen: function () {
      return Math.floor((((this.currItem - 1) / this.itemTotal) * 10000) / 100)
    },
    userListFirstData() {
      if (this.userListFirstTarget) {
        return this.userList[0].expectedOptionNbr
      }
    }
  },
  created() {
    this.getEvalInfoFun()
    this.getItemInfoFun()
    // this.$getDocList(['EMERGENCY,IMPORTANCE']).then(res => {
    //   console.log(res)
    //   this.emerOptions = res.EMERGENCY
    //   this.impOptions = res.IMPORTANCE
    // })

      
    // let params = ['EMERGENCY,IMPORTANCE'].join(',')
    // getDictList({
    //   dictId: params,
    // }).then((res) => {
    //   console.log(res);
    //   if (res.code == 200) {
    //     // res = res.data;
    //     this.emerOptions = res.EMERGENCY
    //     this.impOptions = res.IMPORTANCE
    //   }
    // });
    getDictItem({
      dictId: "EMERGENCY",
    }).then((res) => {
      if (res.code == 200) {
        // res = res.data;
        this.emerOptions = res.EMERGENCY
      }
    });
    getDictItem({
      dictId: "IMPORTANCE",
    }).then((res) => {
      if (res.code == 200) {
        this.impOptions = res.IMPORTANCE
      }
    });

    
  },
  mounted() {},
  methods: {
    // 返回
    goBackBtn() {
      if (this.type == 'eval') {
        //回测评项目列表
        this.$router.push({
          path: '/talentAssessment/talentAssessmentManagement/evaluationItemList'
        })
      } else {
        //回参与建模列表
        this.$router.push({
          path: '/talentAssessment/modelPart'
        })
      }
    },
    //获取测评title
    getEvalInfoFun() {
      getEvalInfo({
        evalId: this.lastBuildId
      }).then(res => {
        // console.log(res)
        this.pageTitle = res.evalName
      })
    },
    // 获取答题信息
    getItemInfoFun() {
      getItemInfo({
        currentItemId: this.currentItemId,
        evalId: this.lastBuildId
      }).then(res => {
        console.log(res)
        this.startTime = res.startTime
        this.componentName = res.componentName
        this.componentDesc = res.componentDesc
        this.itemName = res.itemName
        this.itemDesc = res.itemDesc
        this.answerOptions = res.optionList
        this.userList = res.objectList
        this.currItem = res.currentItemIndex
        this.nextItemId = res.nextItemId
        this.preItemId = res.preItemId
        this.itemId = res.itemId
        this.itemTotal = res.itemTotal
        this.submitStatus = true
        this.userList.map(item => {
          item.importance = item.importance || '3'
          item.emergency = item.emergency || '2'
        })
        this.userListFirstTarget = false
        if (
          this.userList.length > 0 &&
          this.userList[0].relationType == 'U' &&
          (this.userList[0].expectedOptionNbr == 0 || this.userList[0].expectedOptionNbr == null)
        ) {
          this.userListFirstTarget = true
        }
        this.$nextTick(() => {
          this.$refs.answerPage.scroll(0, 0)
          this.$refs.answerTbody.scroll(0, 0)
        })
      })
    },
    // 格式化tooltip显示
    formatTooltip(val) {
      let a = 100 / this.answerOptions.length
      return Math.floor(val * a)
    },
    // 提交当前题目
    submitItem(btnType) {
      if (!this.submitStatus) return
      let evalSubmitRequestList = []
      for (let index = 0; index < this.userList.length; index++) {
        let user = this.userList[index]
        if (!user.optionNbr) {
          this.$msg.warning(`请选择 ${user.objectName} 的实际得分`)
          this.submitStatus = true
          return
        }
        if (user.relationType == 'U') {
          if (!user.expectedOptionNbr) {
            this.$msg.warning(`请选择 ${user.objectName} 的目标得分`)
            this.submitStatus = true
            return
          }
          if (!user.emergency) {
            this.$msg.warning(`请选择 ${user.objectName} 的紧急度`)
            this.submitStatus = true
            return
          }
          if (!user.importance) {
            this.$msg.warning(`请选择 ${user.objectName} 的重要度`)
            this.submitStatus = true
            return
          }
        }
        this.submitStatus = false
        evalSubmitRequestList.push({
          evalId: this.lastBuildId,
          expectedOptionNbr: user.expectedOptionNbr,
          itemId: this.itemId,
          emergency: user.emergency,
          importance: user.importance,
          objectId: user.objectId,
          optionNbr: user.optionNbr,
          postCode: user.postCode,
          startTime: this.startTime
        })
      }
      console.log(evalSubmitRequestList)
      saveEvalSubmit(evalSubmitRequestList).then(res => {
        if (res.code == 200) {
          if (btnType == 'next') {
            if (this.nextItemId == 0) {
              this.submitStatus = true
              // 跳转页面至发展规划
              console.log('跳转页面')
              // this.$router.push(
              //   `/talentAssessment/personalSummary?evalId=${this.lastBuildId}`
              // );
              this.answerSubmitFun()
              return
            }
            this.currentItemId = this.nextItemId
          }
          if (btnType == 'prev') {
            this.currentItemId = this.preItemId
          }
          this.getItemInfoFun()
        } else {
          this.$msg.error(res.msg)
          this.submitStatus = true
        }
      })
    },
    answerSubmitFun() {
      answerSubmit({ evalId: this.lastBuildId }).then(res => {
        if (res.code == 200) {
          this.$router.push('/talentAssessment/talentAssessmentManagement/evaluationItemList')
        } else {
          this.$msg.error(res.msg)
        }
      })
    },
    next() {
      this.submitItem('next')
    },
    prev() {
      this.$confirm('是否保存当前选项？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '保存',
        cancelButtonText: '放弃',
        type: 'warning'
      })
        .then(() => {
          this.submitItem('prev')
        })
        .catch(action => {
          console.log(action)
          this.$msg.info({
            message: action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步'
          })
          if (action == 'cancel') {
            this.currentItemId = this.preItemId
            this.getItemInfoFun()
          }
        })
    },
    submit() {
      // if (
      //   (this.unAnswerItemIds.length == 1 &&
      //     this.unAnswerItemIds[0] == this.itemId) ||
      //   this.unAnswerItemIds.length == 0
      // ) {
      //   // 所有题目都答完
      //   this.goBackBtn();
      // } else {
      //   // 还有题目没答完
      //   this.currentItemId = null;
      //   this.getUserModuleFun();
      // }
    }
  },
  watch: {
    userListFirstData(val, valOld) {
      if (valOld == 0 && this.userListFirstTarget) {
        for (let i = 0; i < this.userList.length; i++) {
          if (i > 0 && this.userList[i].relationType == 'U') {
            this.userList[i].expectedOptionNbr = val
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.answer_content {
  // border: 1px solid #ebebeb;
  color: #303639;
  .answer_table_wrap {
    max-height: 450px;
    overflow: auto;
    margin-top: -1px;
    -webkit-overflow-scrolling: touch; // 为了滚动顺畅

    &::-webkit-scrollbar {
      width: 4px;
    }
  }
  .answer_table {
    width: 100%;
    overflow: auto;
    border-spacing: 1px;
    border-collapse: collapse;
    font-size: 14px;
    color: #303639;
    text-align: center;
    .index {
      width: 50px;
    }
    .post {
      width: 200px;
    }
    .name {
      width: 100px;
    }
    .score {
      width: 50px;
    }
    .imp_emer {
      width: 115px;
      width: 210px;
      padding: 0 10px;
      line-height: 26px;
      font-weight: bold;
      &.importance {
        width: 280px;
      }
    }
    .answer_option {
      position: relative;
      display: flex;
      color: #303639;
      font-weight: bold;
      padding: 0 20px;
      border-left: none;
      .option_item {
        flex: 1;
        border-right: 1px solid #ebebeb;
        &:last-of-type {
          border: none;
        }
      }
      .icon {
        transform: translate(0, -6px);
        color: #0080e4;
      }
    }
    thead {
      tr {
        background: #f9f9f9;
        &.score_tr {
          background: transparent;
          border-bottom: 1px solid #ebebeb;
          .score {
            background-color: #f3f9ff;
          }
        }
        th {
          // height: 40px;
          line-height: 40px;
          font-weight: normal;
          border: 1px solid #ebebeb;
          border-bottom: none;
        }
        .score_wrap {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          z-index: 2;
          padding: 0 20px;
        }
        .border_dom {
          // .score_wrap;
          z-index: 1;
          color: #fff;
          align-items: stretch;
          background-color: #f3f9ff;
          .border {
            flex: 1;
            border-right: 1px solid #ebebeb;
            &:last-of-type {
              border: none;
            }
          }
        }
      }
    }
    tbody {
      tr {
        // height: 40px;
        line-height: 40px;
        td {
          border: 1px solid #ebebeb;
        }
        .name {
          line-height: 28px;
        }
        .score {
          background-color: #f3f9ff;
        }
        .score_bar_box {
          .score_bar_wrap {
            padding: 0 20px;
            ::v-deep .el-slider__runway {
              height: 18px;
              border-radius: 8px;
              .el-slider__bar {
                height: 18px;
                border-radius: 8px;
              }
              .el-slider__button {
                margin: 10px 0 0 0;
                width: 25px;
                height: 25px;
              }
            }
          }
        }
        .target_score_text {
          border-bottom: 1px solid #ebebeb;
        }
      }
    }
    .target {
      ::v-deep .el-slider__bar {
        background: #50d13a;
      }
      ::v-deep .el-slider__button {
        border-color: #50d13a;
      }
    }
  }

  // *******
  .answer_head {
    line-height: 38px;
    // height: 39px;
    text-align: center;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-bottom: 1px solid #ebebeb;
    background-color: #f9f9f9;
    .head_item_level {
      flex: 1;
      border-right: 1px solid #ebebeb;
      font-weight: bold;
    }
    .head_item {
      flex: 0 1 auto;
      height: 100%;
      border-right: 1px solid #ebebeb;
      &.index {
        width: 50px;
      }
      &.post {
        width: 150px;
      }
      &.name {
        width: 100px;
      }
      &.score {
        width: 50px;
      }
    }
  }
  .answer_score_wrap {
    // .answer_head;
    background-color: transparent;
    .border_empty {
      // .head_item;
      color: #fff;
      &.score {
        color: #303639;
        font-weight: bold;
        background-color: #f3f9ff;
      }
    }
    .score_wrap {
      position: relative;
      flex-grow: 1;
      display: flex;
      background-color: #f3f9ff;
      .border_empty {
        flex: 1;
        color: #fff;
      }
      .score_item_content {
        position: absolute;
        width: 100%;
        // height: calc(100% - 1px);
        left: 0;
        top: 0px;
        display: flex;
        justify-content: space-between;
        .score_item {
          font-size: 14px;
        }
      }
    }
  }
  .answer_user_wrap {
    .user_item {
      // .answer_head;
      .user_info {
        background-color: #fff;
        // .head_item;
      }
      .score_box_wrap {
      }
    }
  }
}

.page_section {
}
.answer_progress_wrap {
  padding: 14px 18px 24px;
  border: 1px solid #d8d8d8;
  border-radius: 10px;
  margin-bottom: 30px;
  .progress_text {
    margin-bottom: 28px;
    color: #303639;
    font-size: 14px;
    .current_progress {
      color: #acacac;
    }
    span {
      color: #0080e4;
    }
  }
  .progress_bar {
    position: relative;
    .progress_tip {
      position: absolute;
      bottom: 10px;
      padding: 4px 6px;
      font-size: 14px;
      color: #fff;
      background: #0080e4;
      border-radius: 4px;
      transform: translateX(-50%);
      .arrow_icon {
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        color: #0080e4;
      }
    }
  }
}
.answer_surplus_wrap {
  .assembly_name {
    font-size: 18px;
    color: #2d2d2d;
    font-weight: bold;
    margin-bottom: 20px;
    span {
      color: #449cff;
    }
  }
  .assembly_desc {
    font-size: 14px;
    color: #5b5b5b;
    margin-bottom: 26px;
    line-height: 24px;
  }
  .question_name {
  }
  .question_desc {
  }
}
</style>

<style scoped lang="scss">
.model_answer_wrap {
  .model_answer_tips {
    margin: 0 16px 0 16px;
    padding: 0 10px 0;
    color: #ce837a;
    background: #f7efee;
    font-size: 10px;
    line-height: 20px;
    .el-icon-close {
      height: 20px;
      line-height: 20px;
      cursor: pointer;
    }
  }
  .page_section {
    .answer_surplus_wrap {
      .answer_surplus_info_wrap {
        height: 30px;
        line-height: 30px;
        .unanswered {
          span {
            font-size: 16px;
            color: #ed6942;
            font-weight: 700;
            display: inline-block;
          }
        }
      }
    }
    .answer_surplus_list {
      li {
        margin: 0px 10px 0 0;
        display: inline-block;
        height: 30px;
        line-height: 30px;
        color: #c9c9c9;
        cursor: pointer;
        i {
          margin: 0 3px 0 0;
          line-height: 40px;
          vertical-align: middle;
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #c9c9c9;
        }
      }
      .check_UnAnswer {
        color: #449cff;
        i {
          background: #449cff;
        }
      }
    }
    .ablitity_dict_warp {
      margin: 5px 0 0 0;
      padding: 13px 17px;
      background: #ebf4ff;
      .ablitity_dict_title {
        width: 113px;
        height: 30px;
        font-size: 16px;
        font-weight: bold;
        color: #009aff;
      }
      .dict_list_wrap {
        p {
          .icon {
            // text-align: left;
            margin: 0 0 0 -5px;
            cursor: pointer;
            i {
              font-size: 19px;
              color: #449cff;
            }
          }
          .line {
            flex: 1;
            margin: 0 0 0 5px;
            display: inline-block;
            height: 12px;
            border-bottom: 1px solid #c8e1ff;
          }
        }
        .dict_list {
          margin: 15px 0 0 0;
          li {
            margin: 0 10px 0 0;
            display: inline-block;
            // width: 125px;
            height: 35px;
            color: #a0a0a0;
            cursor: pointer;
            i {
              margin: 0 8px 0 0;
              line-height: 40px;
              vertical-align: middle;
              display: inline-block;
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background: #c9c9c9;
            }
          }
          .finish_check {
            color: #8cda9e;
            i {
              background: #8cda9e;
            }
          }
          .active_check {
            color: #009aff;
            i {
              background: #009aff;
            }
          }
        }
      }
    }
  }
}
.step_bar_wrap {
  .step_item {
    color: #b4d3f9;
  }
  .step_item::before {
    background: #d4e5fa;
  }
  .step_item::after {
    background: #d4e5fa;
  }
  .step_item_icon_wrap {
    .step_item_icon {
      background: #d4e5fa;
      .icon_num {
        background: #b4d3f9;
      }
    }
  }
  .completed {
    .step_item_icon_wrap .step_item_icon {
      background: #b0e6bc;
      .icon_num {
        background: #8cda9e;
      }
    }
    .step_text {
      color: #8cda9e;
    }
    &::after,
    &::before {
      background-color: #b0e6bc !important;
    }
  }
  .inProgress {
    .step_item_icon_wrap .step_item_icon {
      background: #449cff;
      .icon_num {
        background: #449cff;
      }
    }
    .step_text {
      color: #449cff;
    }
    &::after,
    &::before {
      background-color: #0099ff !important;
    }
  }
}
</style>
