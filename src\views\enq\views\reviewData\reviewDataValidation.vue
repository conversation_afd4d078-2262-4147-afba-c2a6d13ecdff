<template>
  <div class="talent_review_report_wrap">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>盘点数据数据确认</p>
        <div class="check_title" v-if="enqName"><span>/</span>{{ enqName }}</div>
      </div>
      <div class="goback_geader" @click="goback"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="talent_review_report_center clearfix">
        <tabsDiffentPane :isDefaultTheme="true" :tabsData="tabsData" @tabsChange="tabsChange"></tabsDiffentPane>
        <div class="pane_content">
          <keep-alive>
            <component :is="tabsPaneName" :enqId="enqId"></component>
          </keep-alive>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import tabsDiffentPane from '@/components/talent/tabsComps/tabsDiffentPane'
import rDataQualityEvaluation from './reviewDataValidationCom/rDataQualityEvaluation.vue'
import rDataPerformanceEvaluation from './reviewDataValidationCom/rDataPerformanceEvaluation.vue'
import { useUtils } from '@/utils/utils'

const route = useRoute()
const utils = useUtils()

const enqId = ref(route.query.enqId)
const enqName = ref(route.query.enqName)
const tabsPaneName = ref('rDataQualityEvaluation')
const tabsData = ref([
  {
    label: '素质评价',
    name: 'rDataQualityEvaluation'
  },
  {
    label: '业绩评价',
    name: 'rDataPerformanceEvaluation'
  }
])

const tabsChange = data => {
  tabsPaneName.value = data.name
}

const goback = () => {
  utils.goback()
}
</script>

<style scoped lang="scss"></style>
