<template>
  <div class="business_area_wrap bg_write">
    <div class="page_main_title">业务领域设置</div>
    <div class="page_section">
      <div class="oper_btn_wrap">
        <el-button class="page_add_btn" type="primary" @click="addItem">新增</el-button>
      </div>
      <div class="business_area_center clearfix">
        <div class="edu_info_header">
          <div class="item">名称</div>
          <div class="item">类型</div>
          <div class="item">状态</div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <template v-if="businessAreaItemData.length > 0">
            <div class="edu_info_item" v-for="(item, index) in businessAreaItemData" :key="item.id">
              <el-input class="item" v-model="item.bizDomainName" placeholder />
              <el-select class="item" v-model="item.dataType" placeholder="请选择类型" disabled>
                <el-option v-for="opt in typeOptions" :key="opt.codeName" :label="opt.codeName" :value="opt.dictCode" />
              </el-select>
              <el-select class="item" v-model="item.status" placeholder="请选择状态">
                <el-option
                  v-for="opt in stateOptions"
                  :key="opt.codeName"
                  :label="opt.codeName"
                  :value="opt.dictCode"
                />
              </el-select>
              <div class="item item_icon_wrap">
                <el-icon class="item_icon" @click="deleteItem(item.companyId, item.bizDomainCode, index)">
                  <Delete />
                </el-icon>
              </div>
            </div>
          </template>
          <div class="no_data_tip" v-else>暂无数据</div>
        </div>
      </div>
      <div class="align_center paddT_30" v-if="businessAreaItemData.length > 0">
        <el-button class="page_confirm_btn" type="primary" @click="submit">确认</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { getDomainList, createDomain, deleteDomain } from '../../request/api'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// 响应式状态
const rStatus = ref('')
const businessAreaItemData = ref([])

// 选项数据
const typeOptions = [
  { codeName: '系统内置', dictCode: 'B' },
  { codeName: '企业定制', dictCode: 'C' }
]

const stateOptions = [
  { codeName: '启用', dictCode: 'Y' },
  { codeName: '关闭', dictCode: 'N' }
]

// 业务领域下拉框
const getDomainListFun = async () => {
  try {
    const res = await getDomainList({
      companyId: companyId.value,
      rStatus: rStatus.value
    })
    if (res.code == 200) {
      if (res.data.length > 0) {
        businessAreaItemData.value = res.data.map(item => ({
          bizDomainCode: item.bizDomainCode,
          bizDomainName: item.bizDomainName,
          companyId: item.companyId,
          dataType: item.dataType,
          status: item.rstatus,
          sortNbr: item.sortNbr
        }))
      } else {
        businessAreaItemData.value = []
      }
    } else {
      businessAreaItemData.value = []
    }
  } catch (error) {
    console.error('获取业务领域列表失败:', error)
    ElMessage.error('获取业务领域列表失败')
    businessAreaItemData.value = []
  }
}

// 新增项目
const addItem = () => {
  const addObj = {
    companyId: companyId.value,
    bizDomainCode: '',
    bizDomainName: '',
    dataType: 'C',
    status: '',
    sortNbr: ''
  }
  if (businessAreaItemData.value.length > 0) {
    const lastItem = businessAreaItemData.value[businessAreaItemData.value.length - 1]
    if (!lastItem.bizDomainName) {
      ElMessage.warning('请完善当前信息后新增！')
      return
    }
  }
  businessAreaItemData.value.push(addObj)
}

// 提交
const submit = async () => {
  if (businessAreaItemData.value.length == 0) {
    ElMessage.warning('请新增后提交！')
    return
  }
  const lastItem = businessAreaItemData.value[businessAreaItemData.value.length - 1]
  if (!lastItem.bizDomainName) {
    ElMessage.warning('请完善信息后提交！')
    return
  }
  await createDomainFun()
}

// 创建或更新业务领域
const createDomainFun = async () => {
  try {
    const res = await createDomain(businessAreaItemData.value)
    if (res.code == 200) {
      ElMessage.success(res.msg)
      await getDomainListFun()
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('保存业务领域失败:', error)
    ElMessage.error('保存业务领域失败')
  }
}

// 删除项目
const deleteItem = async (companyId, code, index) => {
  try {
    await ElMessageBox.confirm('确认删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    if (code) {
      await deleteDomainFun(companyId, code)
    } else {
      businessAreaItemData.value.splice(index, 1)
    }
  } catch (error) {
    // 用户取消删除，不做处理
  }
}

// 删除业务领域
const deleteDomainFun = async (companyId, code) => {
  try {
    const res = await deleteDomain({ companyId, bizDomainCode: code })
    if (res.code == 200) {
      await getDomainListFun()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('删除业务领域失败:', error)
    ElMessage.error('删除业务领域失败')
  }
}

// 初始化获取数据
getDomainListFun()
</script>

<style scoped>
.business_area_wrap {
}

.business_area_center {
  margin-top: 20px;
}

.edu_info_header {
  display: flex;
  background: #f5f7fa;
  line-height: 40px;
  font-weight: bold;
}

.edu_info_item {
  display: flex;
  margin-top: 10px;
}

.item {
  flex: 1;
  margin: 0 10px;
}

.item_icon_wrap {
  flex: 0 0 100px;
  text-align: center;
}

.item_icon {
  cursor: pointer;
  color: var(--el-color-danger);
}

.no_data_tip {
  text-align: center;
  color: #909399;
  padding: 30px 0;
}

:deep(.el-input__inner) {
  width: 100%;
}

.paddT_30 {
  padding-top: 30px;
}
</style>
